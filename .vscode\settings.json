{
// Latex workshop 
 
"latex-workshop.latex.recipes": [
    {
        "name": "latexmk (xelatex)",  //放在最前面为默认编译方案
        "tools": [
            "latexmk-xelatex"
        ]
    },
    {
        "name": "latexmk (pdflatex)",
        "tools": [
            "latexmk-pdflatex"
        ]
    },
	{
        "name": "xelatex",
        "tools": [
            "xelatex"
        ]
    },
    {
      "name": "texify",  //适用于MikTex
      "tools": [
        "texify"
      ]
    },
    {
        "name": "xe->bib->xe->xe",
        "tools": [
            "xelatex",
            "bibtex",
            "xelatex",
            "xelatex"
        ]
    }
  ],
  
  "latex-workshop.latex.tools": [
    {
        "name": "latexmk-xelatex",
        "command": "latexmk",
        "args": [
            "-xelatex",
            "-synctex=1",
            "-interaction=nonstopmode",
            "-file-line-error",
            "%DOC%"
        ]
    },
    {
        "name": "latexmk-pdflatex",
        "command": "latexmk",
        "args": [
            "-pdf",
            "-synctex=1",
            "-interaction=nonstopmode",
            "-file-line-error",
            "%DOC%"
        ]
    },
    {
      "name": "texify",
      "command": "texify",
      "args": [
        "--synctex",
        "--pdf",
        "--tex-option=\"-interaction=nonstopmode\"",
        "--tex-option=\"-file-line-error\"",
        "%DOC%.tex"
      ]
    },
	{
        // 编译工具和命令
        "name": "xelatex",
        "command": "xelatex",
        "args": [
            "-synctex=1",
            "-interaction=nonstopmode",
            "-file-line-error",
            "%DOC%"
        ]
    },
    {
        "name": "pdflatex",
        "command": "pdflatex",
        "args": [
            "-synctex=1",
            "-interaction=nonstopmode",
            "-file-line-error",
            "%DOC%"
        ]
    },
    {
        "name": "bibtex",
        "command": "bibtex",
        "args": [
            "%DOCFILE%"
        ]
    }
],
"workbench.colorTheme": "Default Light+",
"latex-workshop.latex.clean.enabled": true,
"latex-workshop.view.pdf.viewer": "external",
"latex-workshop.view.pdf.external.viewer.command": "C:/Users/<USER>/AppData/Local/SumatraPDF/SumatraPDF.exe",
"latex-workshop.view.pdf.external.viewer.args": [
    "%PDF%"
]
 
}
