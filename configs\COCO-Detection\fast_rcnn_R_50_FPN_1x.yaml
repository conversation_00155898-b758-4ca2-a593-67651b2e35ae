_BASE_: "../Base-RCNN-FPN.yaml"
MODEL:
  WEIGHTS: "detectron2://ImageNetPretrained/MSRA/R-50.pkl"
  MASK_ON: False
  LOAD_PROPOSALS: True
  RESNETS:
    DEPTH: 50
  PROPOSAL_GENERATOR:
    NAME: "PrecomputedProposals"
DATASETS:
  TRAIN: ("coco_2017_train",)
  PROPOSAL_FILES_TRAIN: ("detectron2://COCO-Detection/rpn_R_50_FPN_1x/137258492/coco_2017_train_box_proposals_21bc3a.pkl", )
  TEST: ("coco_2017_val",)
  PROPOSAL_FILES_TEST: ("detectron2://COCO-Detection/rpn_R_50_FPN_1x/137258492/coco_2017_val_box_proposals_ee0dad.pkl", )
DATALOADER:
  # proposals are part of the dataset_dicts, and take a lot of RAM
  NUM_WORKERS: 2
