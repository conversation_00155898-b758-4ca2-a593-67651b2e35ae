1
An advanced Universal Framework for Individual Tree Canopy Segmentation and Monitoring 1
<PERSON><PERSON><PERSON>,1, <PERSON><PERSON><PERSON>,c,1, <PERSON><PERSON><PERSON>,*, <PERSON><PERSON><PERSON>,*, <PERSON><PERSON><PERSON>,*, <PERSON>, 2 <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> 3
4
a Central South University of Forestry and Technology, Hunan 410004, China 5
b Rubber Research Institute, Chinese Academy of Tropical Agricultural Sciences, Haikou, Hainan 571101, China 6
c Central South University, Changsha, Hunan 410083, China 7
d Chinese Academy of Forestry, Beijing 100080, China 8
* Corresponding authors. 9
E-mail addresses: <EMAIL>(J<PERSON>),lv<PERSON><PERSON><EMAIL>(M. Lv),<EMAIL>(<PERSON><PERSON>),hnwx10 <EMAIL>(<PERSON><PERSON>),<EMAIL>(<PERSON><PERSON>),<EMAIL>(<PERSON><PERSON>), <EMAIL>(<PERSON><PERSON>),t20162306@c11 suft.edu.cn(<PERSON><PERSON> <PERSON>) ,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@csuft.edu.cn(<PERSON><PERSON>),<EMAIL>(<PERSON><PERSON>),<EMAIL>(<PERSON><PERSON>) 12
1 <PERSON>quan <PERSON>g and <PERSON>Jie Lv contributed equally to this work. 13
Abstract 14
Forest ecosystem monitoring plays a critical role in the context of global climate change and ecological conservation. However, the inherent 15 complexity of forest ecosystems, characterized by biodiversity and phenological dynamics, presents substantial challenges in forest enumeration and 16 health assessments. Conventional vegetation index (VI) techniques are prone to substantial accuracy degradation under extreme weather conditions, 17 primarily due to large-scale canopy disruption that unveils exposed soil surfaces. This exposure introduces significant spectral contamination, which 18 in turn diminishes the reliability and interpretability of VI-derived ecological metrics. To overcome the aforementioned limitations, we introduce the 19 Canopy Segmentation and Analysis Framework (CSAF), a deep learning–based system that couples high-resolution UAV imagery with precise 20 delineation of individual tree crowns. In parallel, we develop a new vegetation index computation strategy—termed the Single-Canopy Vegetation 21 Index (SCVI)—which enables fine-scale quantification of canopy-level vegetation dynamics. By synergistically integrating its constituent modules, 22 the CSAF framework facilitates accurate, scalable monitoring of forest ecosystems across varying spatial and temporal scales. Its robustness was 23 thoroughly evaluated across four ecologically distinct sites and subsequently extended to characterize forest responses under extreme climatic 24 perturbations—marking one of the first applications of such a system in post-disturbance ecological assessment. Empirical evaluations confirm that 25 the CSAF framework reliably produces accurate estimations of individual tree crowns across temporal sequences, maintaining robustness under 26 varying phenological stages and interspecific variability. Notably, in scenarios characterized by substantial bare soil exposure, the SCVI approach 27 significantly outperforms traditional spectral index computation (SIC) methods by mitigating spectral contamination and enhancing the fidelity of 28 vegetation signal retrieval. Collectively, these capabilities underscore CSAF’s potential as a resilient and scalable solution for monitoring forest 29 ecosystems at broad spatial scales and across ecologically diverse landscapes. 30
Keywords: Deep learning; Instance Segmentation; ecological monitoring; Tree counting; 31
Subject: Computer Sciences, Environmental Sciences (Physical Sciences and Biological Sciences) 32
Highlights 33
1. We propose CSAF, a unified deep learning framework that integrates GM-Mamba, MASA-Optimizer, and MPC-Poisson to improve crown 34 delineation and model adaptability. 35
2. CSAF achieves superior tree counting accuracy across diverse spatial, temporal, and ecological conditions, significantly outperforming eleven 36 state-of-the-art models. 37
3. We introduce SCVI, a crown-based vegetation index method that improves sensitivity and accuracy in post-disaster environments with widespread 38 soil exposure. 39
4. The proposed framework supports a multi-scale forest monitoring paradigm, enabling seamless assessment from regional trends to individual-tree 40 and pixel-level analysis. 41
5. Comprehensive evaluations across six benchmark datasets demonstrate CSAF’s robustness and generalization in highly heterogeneous forest 42 environments. 43
2
1.Introduction 44
Globally, forest ecosystems are encountering mounting pressures due to a combination of escalating climate warming, prolonged drought 45 episodes, increased pest outbreaks, and intensifying climate-related disruptions. The cumulative impact of these stressors is progressively 46 compromising the structural stability and ecological functions of forest biomes, including those with historically high resilience (Cheng et al., 2024). 47 Indeed, even in forest ecosystems widely regarded as resilient—such as those in the Amazon basin—the restoration of biomass stocks and the 48 reestablishment of carbon sequestration capacities may require several decades, or potentially extend over centuries (Trumbore et al., 2015). In 49 addition to its ecological implications, forest degradation exerts substantial socio-economic impacts, particularly on populations whose livelihoods 50 and cultural heritage are intricately tied to forest ecosystems (Walker, 2024). These cascading effects underscore the critical demand for real-time and 51 high-fidelity ecological monitoring technologies to inform timely mitigation and adaptive management strategies. 52
The advent of deep learning has provided a novel approach, enhancing the precision and efficiency of forest surveys while offering critical support 53 for applications such as carbon stock assessment, biodiversity research, and pest monitoring. Studies have demonstrated that deep learning-based 54 canopy segmentation techniques exhibit robust performance in complex forest environments, making them powerful tools for ecological monitoring 55 of forests (Brandt et al., 2020; Li et al., 2023; Zhang et al., 2021). In counting tasks, such techniques enable the accurate identification of individual 56 trees by automatically extracting canopy features and projection contours, effectively distinguishing adjacent tree boundaries and significantly 57 improving the accuracy of individual tree counts. In contrast, traditional manual plot methods are often influenced by terrain complexity and subjective 58 human factors, making it challenging to maintain consistency and efficiency in large-scale environments(Mugabowindekwe et al., 2023; Tollefson, 59 2009). Canopy segmentation and counting not only facilitate efficient large-scale forest resource surveys but also support more detailed analytical 60 tasks(Middleton, 2023; Narut Soontranon et al., 2014). In canopy health monitoring, quantitative analysis of segmentation results enables the 61 monitoring of individual tree growth, health status, and early pest and disease warning. In post-disaster assessments, this technology can quickly 62 identify damaged individuals, accurately locate affected areas through pixel-level analysis, and lay the foundation for establishing a new paradigm of 63 "regional monitoring—individual tree localization—pixel-level analysis." 64
In count-based tasks, the model's canopy segmentation capability directly impacts the accuracy of individual tree count estimation. While various 65 methodologies have been developed to address the canopy structural features in different ecological environments, the inherent diversity and 66 complexity of ecosystems continue to present significant challenges in the practical application of segmentation methods, often resulting in boundary 67 suitability issues. Under the traditional image processing framework, (Jing et al., 2012) proposed a segmentation method that integrates scale filtering 68 with the watershed algorithm. This approach demonstrates robust multi-scale adaptability and effectively alleviates over- or under-segmentation issues 69 in low-density forest stands. However, this method relies on Gaussian filtering to suppress branches and non-target structures. While it improves the 70 overall segmentation performance, it inevitably blurs the textural details of the real canopy edges, leading to potential target merging and 71 misclassification, particularly in dense canopy or boundary-obscured areas.(Tong et al., 2021) proposed a segmentation method that integrates gradient 72 binarization with a spatial local maximum strategy, based on WorldView-3 multispectral data. This approach demonstrated strong boundary 73 recognition capabilities in dense forests. However, in scenarios with weak inter-canopy shadows and significant target overlap, the boundary detection 74 mechanism, which relies on spectral gradient variations, remains susceptible to interference from weeds or homogeneous backgrounds, leading to 75 segmentation errors. (Wagner et al., 2018) introduced an extraction strategy combining the rolling ball algorithm with Expectation-Maximization 76 (EM) distribution modeling, based on WorldView-2 imagery. This method exhibited robust shadow suppression and edge enhancement in tropical 77 forests with diverse tree species and significant lighting variations. Nevertheless, when applied to species with small canopy sizes, the reliance on 78 grayscale distribution and morphological operators still resulted in issues such as over-segmentation and insufficient fine boundary recognition. In 79 deep learning-based methods, the StarDist model introduced by (Tong & Zhang, 2025) employs a star-convex polygon morphology prediction 80 mechanism, integrated within a U-Net architecture to achieve end-to-end modeling of tree crown geometry. This approach significantly enhances 81 segmentation accuracy in mixed forest scenarios. However, its training data is derived from a single region, which, although encompassing multiple 82 tree species, is limited in ecological diversity and phenological stages, thus its robustness across urban forests, subtropical areas, or images captured 83 during the leaf-off period has not been fully validated. Furthermore, the StarDist model's heavy reliance on the star-convex assumption makes it prone 84 to misidentifications and boundary misclassifications when dealing with crossing branches or non-closed shapes. Notably, (Veras et al., 2022) 85 introduced a segmentation framework that integrates multi-seasonal UAS imagery with Convolutional Neural Networks (CNNs) to improve model 86 adaptability across phenological periods. However, due to CNNs’ susceptibility to catastrophic forgetting when faced with tree crowns exhibiting 87
3
varying forms and spectral characteristics, the model
’s generalization capability remains uncertain.Particularly in leaf-off periods or sparse forests, 88 where structural boundaries become blurred and annotation consistency declines, the model's reliance on high-noise labels during training exacerbates 89 error accumulation. This, in turn, negatively impacts the model's performance when deployed across regions or phenological periods. 90
In the calculation of vegetation indices, background interference, such as from soil, often affects the accurate representation of vegetation 91 conditions in remote sensing data, thereby impacting the precision of ecological monitoring. To address this issue, various vegetation index methods 92 incorporating adjustment factors have been proposed to mitigate the interference of soil reflectance on vegetation spectral signals. Among these, soil-93 adjusted vegetation indices (e.g., SAVI and MSAVI) (Qi et al., 1994) effectively suppress the influence of bare soil backgrounds by introducing a soil 94 correction factor. These indices demonstrate particularly strong adaptability in areas with low vegetation cover or significant soil exposure. Compared 95 to the traditional NDVI, these indices offer significant advantages in enhancing vegetation signal sensitivity and improving the correlation with ground 96 ecological parameters, such as biomass and Leaf Area Index (LAI). However, SAVI indices face challenges such as uncertainty in soil factor selection 97 and relatively complex calculation structures, with performance improvement being limited in areas of high vegetation cover. To further enhance soil 98 background correction, the Generalized Soil Adjusted Vegetation Index (GESAVI) (Gilabert et al., 2002) introduces a dynamic adjustment factor 99 based on the geometric characteristics of the soil line, improving adaptability across different soil types and brightness conditions. In scenarios with 100 sparse vegetation or significant soil heterogeneity, GESAVI demonstrates superior performance in vegetation sensitivity and LAI inversion accuracy, 101 underpinned by a solid physical foundation and considerable application potential. However, the method still relies on the accurate modeling of soil 102 line parameters, and its computational complexity remains high. When applied to large-scale, multi-source, heterogeneous remote sensing data, the 103 generalization ability of its parameters and the robustness of the algorithm require further improvement, which remains one of the key limitations to 104 its widespread application. 105
Recent progress in tree crown delineation, individual tree detection, and vegetation index computation has substantially improved the precision 106 and reliability of remote sensing-based ecological monitoring(Sun et al., 2022; Wang et al., 2022). Nevertheless, existing methodologies continue to 107 encounter critical limitations when deployed in complex field conditions, particularly in maintaining robustness and consistency across heterogeneous 108 environments and operational contexts. These challenges are mainly concentrated in two areas:(1) Limited generalization of crown segmentation 109 models across species and phenological conditions. Most deep learning-based tree counting approaches rely heavily on remote sensing imagery 110 captured during specific phenophases, resulting in substantial declines in model performance when applied to non-ideal conditions, such as the leaf-111 off season or sparsely foliated canopies. Furthermore, extending models trained in monospecific stands to mixed-species forests remains problematic. 112 Differences in crown structure, spectral reflectance, and color among species—along with interference from non-target vegetation—significantly 113 increase model uncertainty and reduce segmentation accuracy (Morton et al., 2014; Pearson et al., 2013). These limitations hinder the scalability of 114 existing approaches for large-scale, cross-species, and cross-phenophase ecological monitoring.(2) Continued reliance on traditional soil-insensitive 115 indices (SIC) fails to address background interference in crown health assessment. While conventional SIC-based vegetation indices can partially 116 suppress soil background noise and improve accuracy at population scales, their performance is often compromised in heterogeneous environments 117 due to residual environmental noise. In fragmented ecosystems—such as urban green spaces or post-disturbance forest patches—SIC methods are 118 often ineffective at extracting reliable vegetation information. More critically, these indices reflect average regional conditions and lack the capacity 119 to localize individual trees that are diseased or under stress, thus still requiring costly and labor-intensive field validation. This limits their practical 120 applicability in scenarios requiring fine-scale health monitoring and rapid decision-making. 121
In this study, to address the aforementioned challenges of applying deep learning to ecological monitoring, we propose a comprehensive solution 122 termed the Canopy Segmentation and Analysis Framework (CSAF), which integrates a collaborative mechanism of three modules for tree crown 123 segmentation and analysis. As discussed earlier, significant differences in crown characteristics exist across species and phenological stages. During 124 the leaf-off season, in particular, extensive leaf shedding causes crown contours to become indistinct, making their visual features in remote sensing 125 imagery extremely subtle. Consequently, manual annotation becomes increasingly difficult, resulting in high labeling costs and a greater likelihood 126 of annotation errors. Importantly, although phenological variation can affect canopy appearance, the underlying structural characteristics of tree 127 crowns during the leaf-off period tend to remain relatively stable across seasonal cycles. This morphological consistency provides a promising basis 128 for designing continual learning strategies that enable cross-seasonal adaptation(Wang et al., 2024). To further improve counting accuracy, we 129 introduce the Physics-Informed Neural Network (PINN), which leverages physical constraints on canopy morphology to ensure that the model's 130 segmentation is more aligned with the true canopy structure while also reducing interference from non-target canopies with irregular shapes, such as 131
4
weeds(Raissi et al., 2019). Additionally, to more accurately extract strongly connected canopy boundaries, we incorporate the Mamba State Space 132 Model. This module employs a sequence modeling strategy to capture the continuity of canopy features in spatial distribution, further improving 133 boundary extraction performance(Gu & Dao, 2024). 134
To rigorously assess the performance of CSAF, we implemented a series of controlled evaluations across four ecologically diverse regions, 135 utilizing multinational datasets. The segmentation outcomes were quantitatively compared against eleven representative state-of-the-art models, 136 allowing for a robust benchmarking of CSAF’s relative accuracy and generalization capability. Notably, to demonstrate the impact of continuous 137 learning, we conducted experiments on a dataset where leaf-off samples accounted for only 4.7% of the training set. In addition, a generalization 138 experiment was carried out in a mixed-species forest scenario involving 24 tree species, aiming to test the framework’s dynamic adaptability across 139 species and phenological stages. The results demonstrate that CSAF consistently outperforms existing methods across all evaluation metrics. 140 Furthermore, to evaluate CSAF’s tree counting performance under cross-species, cross-phenology, and multi-temporal conditions, we selected the 141 best-performing model from the baseline comparison as a reference and conducted systematic crown counting experiments across different forest 142 regions. Finally, we extended CSAF to extreme climate scenarios and conducted a comparative assessment between SCVI and SIC methods using 16 143 vegetation indices. The experimental results confirm that our framework exhibits superior performance across a wide range of complex conditions. 144 In summary, this study makes the following contributions: 145
1. We constructed three representative forest datasets spanning two years, each corresponding to distinct ecological conditions: (1) a large-scale 146 healthy forest region unaffected by typhoon events, (2) a small-scale localized forest area, and (3) a large-scale forest region severely damaged by 147 typhoons. These datasets encompass diverse canopy structures and disturbance types, providing a robust foundation for subsequent model training 148 and ecological monitoring research. 149
2. We proposed three dedicated modules to address key challenges in tree crown segmentation tasks. Specifically, the GM-Mamba module 150 leverages the long-range modeling capacity of the Mamba network to effectively alleviate the difficulty of identifying blurred crown boundaries. The 151 MASA-Optimizer module regulates the continual learning process through a multi-agent mechanism, enhancing the model’s generalization capability 152 under cross-species and cross-phenological conditions. The MPC-Poisson module leverages PINN-based constraints to impose structural priors on 153 crown morphology, thereby improving robustness against interference from non-target vegetation. 154
3. CSAF incorporates a vegetation index-based segmentation mask mechanism, enabling the model to automatically compute multiple vegetation 155 indices for each crown during the segmentation process. This module is further extendable to support fine-scale vegetation parameter extraction at 156 both pixel and regional levels. Each tree crown is assigned a unique identifier, and the associated index values can be queried and linked through 157 exported CSV files, facilitating the standardization and operational deployment of ecological monitoring data. 158
4. The adaptability and superiority of CSAF were validated through crown counting tasks conducted under multiple ecological environments 159 and across different temporal scales. Experimental results demonstrate that CSAF significantly outperforms existing comparative methods in terms 160 of accuracy, stability, and generalization capability, particularly exhibiting stronger robustness and practical value in long-term and cross-scale 161 counting scenarios. 162
5. A vegetation index calculation method with enhanced resistance to background interference was proposed, which effectively suppresses the 163 influence of non-target factors such as soil. When integrated with CSAF, this method was applied to extreme natural disaster scenarios, supporting 164 both regional-scale ecological change assessment and precise localization of damaged tree crowns with pixel-level analysis. This advancement 165 promotes a shift in ecological monitoring from coarse-scale assessment to individual-level crown diagnosis and fine-grained index interpretation. 166
2. Materials and methods 167
2.1 Study site 168
The study area is located in the northwestern part of Danzhou City, Hainan Province, China, with geographic coordinates of 19°31′50.59″N and 169 109°28′52.62″E. It lies within an experimental forest region established by the Rubber Research Institute of the Chinese Academy of Tropical 170 Agricultural Sciences. The specific location is shown in Fig. 1.This region belongs to the South Asian tropical monsoon climate zone, characterized 171 by warm temperatures with minimal intra-annual variation. The average annual temperature ranges from 22.5°C to 25.6°C, with January being the 172 coldest month and July the hottest. The annual sunshine duration ranges from 1,780 to 2,600 hours, while annual precipitation varies between 900 173 and 2,400 mm, providing favorable hydrothermal conditions for rubber tree growth. It is worth noting that Hainan Island is situated on the northern 174 edge of the South China Sea and is one of the coastal islands frequently affected by typhoons. The primary typhoon season spans from June to October. 175
5
Although Danzhou is located in the northwestern part of the island—far from the typical landfall zones—it is still regularly influenced by tropical 176 cyclones and their peripheral cloud systems. These disturbances, including extreme winds and heavy rainfall, often cause structural damage such as 177 tree lodging and canopy fragmentation. As a typical tropical cash crop, rubber trees have canopy structures that are highly sensitive to wind 178 disturbances. Therefore, conducting field observations and remote sensing studies in this area not only enables the collection of canopy response data 179 under varying disturbance intensities but also provides an ideal testing platform for evaluating the robustness and generalizability of the proposed 180 crown segmentation and ecological monitoring framework under complex background conditions. Furthermore, this area offers a suitable environment 181 for assessing the framework's adaptability and monitoring accuracy in extreme weather scenarios, with great potential for applications in disaster 182 response and forest health assessment. 183 184
Fig. 1 Locations of the Study Areas. 185
2.2 Data set creation 186
We conducted extensive experiments on three self-constructed datasets and three publicly available datasets, encompassing diverse ecosystems 187 ranging from tropical rubber plantations to temperate mixed forests.(1) Rubber Tree Dataset (RT-Set) includes images collected between November 188 3, 2023, and July 8, 2024. This dataset consists of 3,697 training images, 1,056 validation images, and 528 test images, which represent four distinct 189 phenophases: budburst, growth, maturity, and leaf fall. Specifically, the training set contains 1,074, 1,381, 1,068, and 174 samples for each phenophase, 190 respectively; the validation set contains 271, 286, 243, and 256 samples; and the test set includes 131, 136, 120, and 141 samples. This dataset is 191 primarily utilized to evaluate crown segmentation performance.(2) The Counting Plot Dataset (RRA-Set) was collected from March 2023 to March 192 2024 using high-frequency temporal sampling. This dataset consists of 195 images and is primarily intended for evaluating crown count performance. 193 (3) The Post-Disaster Rubber Tree Dataset (DRT-Set) spans from August 9, 2024, to February 6, 2025, and includes images of rubber plantations 194 affected by typhoons. It is primarily used to assess model robustness under extreme weather conditions. All three self-constructed datasets were 195
6
collected from rubber planting regions in Hainan Province, China. Specifically, RT-Set and DRT-Set originate from the same forest area, with DRT-196 Set significantly impacted by natural disasters, while the RRA-Set was collected from an adjacent, smaller forest area. Data collection employed a 197 Phantom 4 RTK UAV, equipped with a 1-inch CMOS sensor (20 MP effective pixels, 20.48 MP total pixels). The flight parameters included a speed 198 of 6 m/s, a flight altitude of 80 meters, and both lateral and longitudinal overlap rates of 85%. Images were captured at equal time intervals, and 199 orthophotos were generated using Pix4DMapper, followed by cropping each dataset into various pixel sizes for training and testing at different scales. 200
In addition, to evaluate the model’s generalization ability across ecologically divergent conditions, three publicly accessible datasets with 201 substantial variation in vegetation types were incorporated. (1) The Bayberry Dataset (BT-Set) (L & W, 2019) was collected in Dayang Mountain 202 Forest Park, Yongjia County, Zhejiang Province, China (28°17′N–28°19′N, 120°26′E–120°28′E), using a DJI Phantom 4 UAV. This area falls within 203 the mid-subtropical evergreen broad-leaved forest zone and includes extensively cultivated Myrica rubra orchards distributed along sun-facing slopes 204 with acidic red soil below 1500 meters elevation. The dataset comprises 1,701 training images, 486 validation images, and 243 test images. Tree 205 crowns were manually annotated using LabelMe with polygon masks. Although the canopy is relatively sparse, strong shadow effects and dense 206 understory vegetation exhibit high spectral similarity to the crowns, making the dataset particularly challenging for instance segmentation tasks. BT-207 Set thus provides a valuable benchmark for evaluating model robustness under weak boundary conditions and visually ambiguous backgrounds. (2) 208 The Urban Tree Canopy Dataset (UT-Set) (Velasquez-Camacho et al., 2023) was constructed to support deep learning–based instance segmentation 209 of urban trees under complex city environments. The dataset originates from Lleida, a Mediterranean city in northeastern Spain (41°37′N, 0°37′E), 210 characterized by heterogeneous urban infrastructure, diverse tree species, and variable lighting conditions. The selected area spans 59 hectares and 211 includes multiple urban contexts, such as major streets, residential areas, public parks, and densely built-up zones, thus offering broad scene diversity 212 and structural occlusion. Aerial orthophotos were acquired using high-resolution airborne sensors and manually labeled using Google Street View 213 (GSV) imagery to ensure high annotation quality. Urban tree crowns were polygonally marked, yielding a total of 14,772 labeled crowns. From this 214 dataset, 994 images were used for training, 284 for validation, and 142 for testing. The annotations cover a wide range of crown shapes and densities, 215 including instances partially occluded by buildings, vehicles, or street furniture, making it a challenging benchmark for evaluating segmentation 216 precision and generalization.Due to the visual similarity between urban tree crowns and other landscape elements (e.g., bushes, shadows, asphalt), 217 models often suffer from over-segmentation or background misclassification. Therefore, UT-Set is particularly suitable for assessing the robustness 218 of instance segmentation models in real-world, highly cluttered urban environments. (3) The Canadian Tree Foliar Phenology Set (CTF-Set) (Cloutier 219 et al., 2024) was collected in a temperate mixed forest located in Saint-Hippolyte, Quebec, Canada (45°59′N, 74°00′W). The study area spans diverse 220 terrain, including hills, wetlands, and lakes, and supports a wide range of deciduous and coniferous species typical of unmanaged North American 221 forests. A key challenge in this dataset arises from the high species diversity within the canopy, encompassing over a dozen tree species and genera 222 with distinct but often overlapping crown structures. In particular, species within the same genus (e.g., Acer rubrum, Acer saccharum) share similar 223 foliar traits, while seasonal variations in leaf coloration and abscission introduce strong intra-class variability. These factors lead to blurred inter-class 224 boundaries and frequent confusion with background elements such as understory vegetation and deadwood, making the dataset particularly demanding 225 for fine-grained crown-level segmentation tasks. This dataset is used exclusively for testing and includes a total of 5,321 images. 226
These multi-source datasets collectively form the foundation for a comprehensive evaluation of the CSAF framework across species, 227 phenological stages, spatiotemporal variations, and extreme scenarios, providing a solid basis for its practical application and widespread adoption in 228 ecological monitoring. 229
2.3 Overall Architecture of the CSAF Framework 230
Tree crown segmentation under real-world ecological conditions remains a substantial challenge due to dynamic species–phenology interactions, 231 indistinct crown boundaries, and interference from morphologically similar non-target vegetation. Conventional instance segmentation approaches 232 often suffer from limited adaptability, particularly during leaf-off seasons or in densely vegetated canopies, where manual delineation is unreliable 233 and inconsistent. To overcome these limitations, we introduce the Canopy Segmentation and Analysis Framework (CSAF), a modular architecture 234 composed of four synergistic components: GM-Mamba for boundary refinement, MASA-Optimizer for continual learning and adaptive optimization, 235 MPC-Poisson for physics-informed crown regularization, and a vegetation index analysis module for quantifying canopy-level spectral dynamics. 236
As illustrated in Fig. 2, the CSAF pipeline begins with preprocessing techniques such as random cropping and brightness adjustment to improve 237 generalization. The image is first input into a lightweight skeleton network to produce an initial feature map. Simultaneously, the GM-Mamba module 238 performs multi-scale edge enhancement and models long-range dependencies. Denoising is achieved via Fourier transform, and high-frequency edge 239
7
responses are captured using Laplacian filtering. Residual encoder blocks then extract deep features at progressively reduced resolutions. 240
The feature maps are flattened into a sequential format 𝜒𝜒∈ℝ𝑏𝑏×𝑙𝑙×𝑑𝑑, where 𝑏𝑏 is the batch size, 𝑙𝑙 is the sequence length, and 𝑑𝑑 is the feature 241 dimension. These are decomposed into primary and residual streams via a projection layer. The primary stream applies 1D convolution and structured 242 state transitions via a State Space Model (SSM), while the residual stream retains contextual information. The outputs are fused into a unified 243 representation and passed to the CDPO-E engine (Coupled Data-driven and Physics-constrained Optimization Engine), composed of MASA-244 Optimizer and MPC-Poisson. 245
The MASA-Optimizer introduces a collaborative multi-agent control strategy to govern a learnable forgetting coefficient 𝛼𝛼, which balances 246 historical knowledge retention with adaptation to new data. The MPC-Poisson module introduces shape-aware physical constraints using the Poisson 247 equation, guiding structural coherence and suppressing spectral interference from background vegetation. A physics-guided loss term is propagated 248 through the MASA-Optimizer, dynamically adjusting 𝛼𝛼 and enabling coordinated optimization across both modules. 249
To enhance temporal adaptability, features retained in the MASA-Optimizer’s experience region are refined using a fusion mechanism guided 250 by simulated annealing, reinforcement learning, and genetic algorithms. This ensures dynamic feature stability and smooth parameter transitions 251 during training. 252
In addition to accurate crown delineation, CSAF supports fine-scale ecological analysis by introducing the Single-Canopy Vegetation Index 253 (SCVI). Unlike conventional region-based indices such as SIC, SCVI utilizes pixel-level statistics confined within crown masks to remove spectral 254 contamination from shadows, soil, and neighboring vegetation. This enables reliable and interpretable vegetation assessments across varied canopy 255 densities and heterogeneous landscape conditions. 256
We now describe each module in further detail. 257 258
Fig. 2 Architecture of the CSAF framework for tree crown segmentation and monitoring. (A) Preprocessing includes cropping, 259 brightness/contrast adjustment, and backbone feature extraction. (B) GM-Mamba enhances crown boundaries via multi-scale gradient 260 encoding and spatial modeling. (C) MASA-Optimizer adopts a multi-agent learning strategy to address catastrophic forgetting across species 261 and phenology. (D) MPC-Poisson embeds shape-aware priors via a Poisson-based constraint to suppress background interference. (E) The 262 outputs include accurate crown masks, vegetation index heatmaps (SCVI), and tree-level data analysis. 263
OutputEMask ImageVegetation Index Heatmap Mask ImageAccurate segmentation of tree crownsExtract tree crowns from segmentation results and visualize health status via a heatmap mask.Data AnalysisFast Fourier TransformLaplacian pyamidMambaGM-MambaBMulti-Layer PerceptronMPC-PoissonDPoisson's EquationpreprocessInputRandom CroppingBrightness AdjustmentContrast AdjustmentABacnboneL2 normalizationReLuL2 normalizationReLuLoss FeedbackAdditionFusionMASA-OptimizerCExperience ReplayFeature ExtractorSimulated AnnealingReinforcement LearningGenetic AlgorithmDiscarding LayerDataStreamMulti-AgentOptimizer
8
2.4 Accurate Delineation of Blurred Tree Crown Contours via GM-Mamba 264
We propose the GM-Mamba module (Fig. 3), which integrates Fourier transform, Laplacian pyramid, and Mamba modeling, to address the 265 challenge of blurred crown contours in dense forest segmentation tasks. In such tasks, image noise significantly degrades segmentation accuracy. 266 Moreover, the Laplacian operator within the Laplacian pyramid is sensitive to noise and cannot effectively suppress it. Therefore, we first apply 267 Fourier transform-based denoising to the image before feeding it into the Laplacian pyramid. By leveraging the edge enhancement capability of the 268 Laplacian operator, crown contours can be effectively accentuated, thereby improving the Mamba module's ability to recognize edge details during 269 long-range dependency modeling. Specifically, we first employ a Fourier transform technique (Oberst, 2007) to convert the image from the spatial 270 domain to the frequency domain(1). In the frequency domain, we perform Fourier transforms separately on each color channel (red, green, and blue) 271 to extract their respective frequency components 272
𝐹𝐹(𝑢𝑢,𝑣𝑣)=∫𝐼𝐼(𝑥𝑥,𝑦𝑦)∙𝑒𝑒−2𝜋𝜋𝜋𝜋(𝑢𝑢𝑢𝑢+𝑦𝑦)𝑑𝑑𝑑 𝑑𝑑𝑑 ∞−∞， (1) 273
Here, 𝑢𝑢 and 𝑣𝑣 denote the frequency domain coordinates, representing the horizontal and vertical frequency components of the image, 274 respectively.𝐹𝐹(𝑢𝑢,𝑣𝑣) is the complex representation of the image in the frequency domain, consisting of a real part 𝑅𝑅𝑅 (𝐹𝐹(𝑢𝑢,𝑣𝑣)) and an imaginary 275 part 𝐼𝐼𝐼𝐼(𝐹𝐹(𝑢𝑢,𝑣𝑣)).The real part (2) and imaginary part (3) are defined as follows: 276
𝑅𝑅𝑅 (𝐹𝐹(𝑢𝑢,𝑣𝑣))=∫∫𝐼𝐼(𝑥𝑥,𝑦𝑦)∙𝑐𝑐𝑐𝑐𝑐𝑐(2𝜋𝜋(𝑢𝑢𝑢 +𝑣𝑣𝑣 ))𝑑𝑑𝑑 𝑑𝑑𝑑 ∞−∞∞−∞ ， (2) 277
𝐼𝐼𝐼𝐼(𝐹𝐹(𝑢𝑢,𝑣𝑣))=∫∫𝐼𝐼(𝑥𝑥,𝑦𝑦)∙𝑠 𝑠𝑠𝑠𝑠(2𝜋𝜋(𝑢𝑢𝑢 +𝑣𝑣𝑣 ))𝑑𝑑𝑑 𝑑𝑑𝑑 ∞−∞∞−∞， (3) 278
The frequency domain representation not only reveals the global structural characteristics of the image but also preserves rich local detail 279 information, providing strong support for subsequent edge enhancement and noise suppression. Next, we feed the real and imaginary parts separately 280 into two Laplacian operators with kernel sizes of 𝑘𝑘3×3 and 𝑘𝑘5×5 (4), respectively, to extract edge information at multiple scales. 281
𝑘𝑘3×3=􀵥1010−40101􀵩，𝑘𝑘5×5=⎣⎢⎢⎢⎡−2−4−4−4−2 −4080−4 −4 8 24 8 −4 −4 0 8 0 −4 −2−4−4−4−2⎦⎥⎥⎥⎤ (4) 282
The real and imaginary parts, after convolution with the two Laplacian operators of different kernel sizes, produce the enhanced frequency maps： 283
𝐹𝐹′(𝑢𝑢,𝑣𝑣)=𝑅𝑅𝑒𝑒(𝑢𝑢,𝑣𝑣)×𝐾𝐾3×3+𝑅𝑅𝑒𝑒(𝑢𝑢,𝑣𝑣)×𝐾𝐾5×5+𝐼𝐼𝑚 (𝑢𝑢,𝑣𝑣)×𝐾𝐾3×3+𝐼𝐼𝑚 (𝑢𝑢,𝑣𝑣)×𝐾𝐾5×5， (5) 284
This process enables dual-scale extraction of both coarse and fine-grained edge features, effectively enhancing edge clarity. Subsequently, a 285 feature refinement stage is performed using fusion operations and convolutional layers. Deep semantic information is extracted via residual blocks, 286 and skip connections are employed to mitigate the vanishing gradient problem, thereby improving the model’s ability to capture image details. 287
In complex canopy structures—particularly under high canopy closure—crowns often overlap and interlace, making it difficult to accurately 288 delineate boundaries. However, the spatial distribution of crown foliage exhibits a distinct long-range dependency: leaves tend to align continuously 289 along the direction of branches, reflecting strong spatial correlation. Conventional convolutional neural networks struggle to capture such long-range 290 spatial dependencies. To address this, we introduce Mamba, a state space model originally developed in the field of natural language processing. 291 Mamba exhibits strong sequence modeling capabilities and is well-suited for handling structured and continuous visual patterns like crown foliage. 292
Specifically, the output feature map is first flattened into a sequence state 𝜒𝜒∈ℝ𝑏𝑏×𝑙𝑙×𝑑𝑑, where 𝑏𝑏 is the batch size, 𝑙𝑙 is the sequence length, and 293 𝑑𝑑 is the feature dimension. This sequence is then passed into the Mamba module. A linear projection layer generates two streams: a processing stream 294 and a residual stream. The processing stream is enhanced via convolution and the SiLU activation function to emphasize local features for state space 295 modeling, while the residual stream passes only through the SiLU activation function to retain the original feature information. The processing stream 296 is subsequently input into the SSM (State Space Model), which constructs a dynamic relational network among pixels based on a state equation (6). 297
𝑥𝑥𝑡𝑡+1=𝐴𝐴𝑥𝑥𝑡𝑡+𝐵𝐵𝑢𝑢𝑡𝑡+𝑤𝑤𝑡𝑡， (6) 298
Here, 𝑥𝑥𝑡𝑡 represents the latent state features of the pixel, while 𝑢𝑢𝑡𝑡 denotes the input feature sequence. The matrix 𝐴𝐴 is a learnable state 299 transition matrix that evolves progressively with the sequence update. The input matrix 𝐵𝐵 establishes the mapping between input features and latent 300 states, and the noise term 𝑊𝑊𝑡𝑡 is introduced to enhance model robustness. Although natural images lack inherent temporal dimensions, by 301 transforming spatial features into pseudo-sequences and applying temporal dynamics from state space models, the framework is capable of simulating 302 sequential dependencies in the spatial domain. This mechanism allows the model to effectively capture long-range pixel-level interactions, thereby 303 enabling a deeper understanding of the directional structural patterns within individual tree crowns and improving boundary discrimination between 304 adjacent crowns. 305
9
After the completion of state modeling, the model needs to transform the latent states into actual pixel-level predictive structures. This process 306 is achieved through the observation equation： 307
𝑦𝑦𝑡𝑡=𝐶𝐶𝑥𝑥𝑡𝑡+1+𝐷𝐷𝑢𝑢𝑡𝑡+𝑣𝑣𝑡𝑡， (7) 308
In this formulation, 𝑦𝑦𝑡𝑡 denotes the observed pixel value, while the learnable matrix 𝐶𝐶 decodes the latent state into pixel-level predictions. The 309 matrix 𝐷𝐷 serves as a residual mapping, preserving information from the input features, and 𝑣𝑣𝑡𝑡 represents the observation noise, which facilitates 310 model generalization. Notably, as our task focuses on individual tree crown segmentation, incomplete crowns within the image are not segmented. 311 To tackle this issue, the State Space Model (SSM) incorporates a Selective Scan strategy that restricts full-state updates exclusively to regions 312 exhibiting high confidence (He et al., 2025; Huang et al., 2025).By doing so, it facilitates a spatially adaptive attention mechanism that can 313 dynamically prioritize informative areas while reducing computational overhead. The assignment of attention weights is governed by the following 314 formulation: 315
𝛿𝛿𝑖𝑖,𝑗 =𝜎𝜎(𝑊𝑊𝛿𝛿∙𝐹𝐹𝑓𝑓𝑓 𝑓𝑓𝑓𝑓𝑓 ((𝑖𝑖,𝑗 ))， (8) 316
When 𝛿𝛿𝑖𝑖,𝑗 >0.7, the pixel is considered to belong to a high-confidence region. During the state update process, the model performs full-state 317 updates on these regions, while adopting a sparse update strategy for the remaining areas. This selective attention mechanism effectively focuses on 318 critical regions, thereby improving modeling efficiency and accuracy. Finally, the dynamic feature sequence output by the SSM is element-wise 319 multiplied with the residual stream to achieve comprehensive fusion between the dynamic and original features. A subsequent linear projection layer 320 further refines the feature representation, which is then reshaped back to the original feature map size and fused with the backbone network's features. 321
With the integration of the GM-Mamba module, the model demonstrates enhanced performance in handling crown boundary ambiguity caused 322 by high canopy closure and crown interlacing in dense forests. The effectiveness of this module in improving the accuracy of individual tree crown 323 detection and segmentation is further validated through ablation studies and visualized results presented earlier. 324 325
Fig. 3 Illustration of the GM-Mamba module for boundary-aware crown segmentation. The module integrates frequency-domain denoising 326 using Fourier transform, multi-scale edge enhancement via Laplacian pyramids, and long-range dependency modeling using a Mamba-based 327 state space model. Dual-scale gradient cues are extracted from real and imaginary components in the frequency domain, enabling coarse-to-328 fine edge detection. The output is passed into a Selective Scan-enhanced SSM, which captures directional crown structures through dynamic 329 state updates. The fused output enhances boundary continuity and crown-level spatial consistency. 330
2.5 Suppressing Non-Target Vegetation Interference via MPC-Poisson Constraints 331
The MPC-Poisson module (Fig. 4) is constructed with multilayer perceptrons (MLPs) that transform the input features through a series of linear 332 projections into probability predictions of canopy presence. By incorporating the Poisson equation, the model is physically constrained to avoid over-333 segmentation of surrounding weeds or misclassification of non-target vegetation, thereby mitigating interference in canopy segmentation tasks.Unlike 334 tree crowns, which generally exhibit a diffusion-like structure (Bochner, 1949), non-target plants such as weeds often have highly irregular shapes. 335 Inspired by this distinction, we introduce a morphological constraint based on the Poisson equation. This constraint targets the second-order 336
FrequencyamplitudeFFTAdditionConv3×3Conv5×5Laplacian pyramidLinearLinearConvSiLuSiLuSSM×LinearMambainputMultiplyBsumState updateCMultiplysumDMultiplyAMultiplySelective ScanOutput
10
differential characteristics of the predicted probability field 𝛾𝛾(𝑥𝑥,𝑦𝑦), and formulates a morphology-aware loss function accordingly.To begin with, the 337 input feature map is resized to a fixed resolution of 160×160 via bilinear interpolation to standardize subsequent computations. Then, we generate 338 normalized x and y coordinate grids, where 𝑥𝑥 ∈ [0,1] spans the image width and 𝑦𝑦 ∈ [0,1] spans the height. These coordinates are evenly 339 distributed across the spatial dimensions, mapping each pixel in the feature map to a specific coordinate pair. Thus, for any given pixel at position 340 (𝑖 ,𝑗𝑗) in the feature map, its corresponding spatial coordinate in the constructed coordinate mesh is defined as (𝑥𝑥𝑗 ,𝑦𝑦𝑖𝑖): 341
𝑥𝑥𝑗 =𝑗 𝑊𝑊−1 ，𝑦𝑦𝑖𝑖=𝑖𝑖𝐻𝐻−1， (9) 342
Here, 𝑖 denotes the row index and 𝑗𝑗 the column index of the feature map. The corresponding spatial coordinates (𝑥𝑥,𝑦𝑦) are then passed into a 343 multilayer perceptron (MLP), which is designed to map each spatial location (𝑥𝑥,𝑦𝑦) to a probability value.Each input coordinate contains two 344 dimensions, representing the normalized horizontal and vertical positions of each pixel. The MLP is constructed with a four-layer architecture, where 345 each layer applies a linear transformation. To enhance the model’s nonlinear representation capability, Swish activation functions are introduced in 346 the first three layers. 347
𝑆𝑆𝑆𝑆𝑆𝑆𝑆𝑆𝑤𝑤𝑠𝑠𝑐𝑐ℎ(𝑥𝑥)=𝑥𝑥∙𝜎𝜎(𝑥𝑥)， (10) 348
Here, σ(x) denotes the Sigmoid function： 349
𝜎𝜎(𝑥𝑥)=11+𝑒 −𝑥𝑥， (11) 350
A final linear transformation is then applied at the output layer： 351
𝛾𝛾=𝑊𝑊4∙𝑆𝑆𝑆𝑆𝑆𝑆𝑆𝑆𝑤𝑤𝑠𝑠𝑐𝑐ℎ(𝑊𝑊3∙𝑆𝑆𝑤𝑤𝑠𝑠𝑐𝑐ℎ(𝑊𝑊2∙𝑆𝑆𝑤𝑤𝑠𝑠𝑐𝑐ℎ(𝑊𝑊3𝜒𝜒+𝑏𝑏1)+𝑏𝑏2)+𝑏𝑏3)+𝑏𝑏4， (12) 352
At this stage, 𝛾𝛾 represents the final output, encapsulating the probability of canopy presence for each individual element. Based on these 353 independent predictions at the element level, a morphological loss function is introduced. Specifically, the spatial distribution of the predicted feature 354 map 𝛾𝛾(𝑥𝑥,𝑦𝑦) is governed by the following equation： 355
D􁉀𝜕𝜕2𝛾𝛾𝜕𝜕𝑥 2+𝜕𝜕2𝛾𝛾𝜕𝜕𝑦𝑦2􁉁+ψ=0， (13) 356
Here, ψ denotes a fixed source term, D is the diffusion coefficient, and 𝑥𝑥 and 𝑦𝑦 represent spatial coordinates. A larger 𝜓𝜓 value corresponds 357 to a greater degree of spatial diffusion within the region, whereas a smaller 𝜓𝜓 imposes stronger suppression of regional continuity. Since non-target 358 vegetation such as weeds often exhibits strong spatial continuity, we set 𝜓𝜓=−3 to enhance the suppression effect on such background noise.The 359 diffusion coefficient D controls the spatial diffusion rate of the canopy morphology. A larger D value promotes stronger diffusion, effectively 360 suppressing local noise but potentially leading to excessive smoothing of canopy boundaries. In contrast, a smaller D preserves more fine-grained 361 details but may allow residual noise to persist. Empirically, non-target vegetation tends to appear as small-scale patches misidentified as tree crowns, 362 dispersed pixel-level false positives, or scattered responses along canopy edges. Mathematically, these patterns are characterized by high-frequency 363 signals, i.e., regions with large second-order spatial derivatives. Therefore, we empirically set D=0.6 to effectively suppress non-target interference 364 while preserving the integrity of canopy edge details. 365
To incorporate the physical constraint during the training process, we compute the second-order spatial derivatives of the predicted output to 366 construct the morphology-aware loss term. Specifically, the physical information loss is formulated as follows within the overall loss function of the 367 model： 368
ℒphys=∂2γ∂x2+∂2γ∂y2−ψD， (14) 369
This loss term ensures that the predicted output adheres to the diffusion behavior. Accordingly, the loss function of CSAF during training is 370 formulated as： 371
ℒ𝑡𝑡𝑡𝑡𝑡𝑡𝑡𝑡𝑡 =ℒ𝑑𝑑𝑑𝑑𝑑𝑑𝑑𝑑𝑡𝑡𝑡𝑡𝑡𝑡+ℒ𝑝𝑝ℎ𝑦𝑦𝑦 ， (15) 372
Here, ℒdata represents the data loss computed between the predicted outputs and the ground truth labels, while ℒphys denotes the physics-373 informed loss. The integration of the MPC-Poisson module effectively reinforces constraints on the model's output masks, thereby mitigating the 374 interference from non-target vegetation. 375
11
376
Fig. 4 Architecture of the MPC-Poisson module for enforcing morphological constraints during crown segmentation. The module maps 377 normalized spatial coordinates through a four-layer MLP, producing a probability field 𝜸𝜸(𝒙𝒙,𝒚𝒚) for canopy presence. A physics-informed 378 loss term derived from the Poisson equation penalizes deviations from expected diffusion patterns, thereby suppressing irregular non-target 379 vegetation and enhancing crown mask coherence. 380
2.6 Adaptive Response to Species–Phenology Dynamics via MASA-Optimizer 381
The dynamic changes in species diversity and phenological stages within forest ecosystems often induce feature drift, significantly complicating 382 canopy segmentation. To address this challenge, we developed the MASA-Optimizer (Fig. 5) and incorporated it into a continual learning framework 383 to enhance the model's adaptability in complex environments. The optimizer consists of a feature extraction module and an experience replay module, 384 which collectively extract and integrate features across multiple temporal scales. However, continual learning inherently faces the trade-off between 385 retaining old knowledge and incorporating new information (Perkonigg et al., 2021; Wei et al., 2022). To effectively mitigate this challenge, we 386 propose a novel, dynamically adjustable forgetting factor, denoted as 𝛼𝛼 , which governs the balance between prior and newly acquired feature 387 representations. This adaptive mechanism is embedded within a three-stage dynamic optimization framework that integrates the strengths of simulated 388 annealing (Rere et al., 2015), reinforcement learning (Vinyals et al., 2019), and genetic search strategies (McCall, 2005). Such a hybrid approach 389 allows the system to fine-tune 𝛼𝛼 , in response to evolving training dynamics, thereby yielding a robust and context-aware fusion of feature 390 representations. The resultant formulation for the fused feature is expressed as: 391
𝛤𝛤𝑛𝑛𝑛 𝑛𝑛 = 𝛤𝛤𝐶𝐶𝐶𝐶𝐶𝐶𝐶𝐶−𝐸𝐸 + (Σ𝛤𝛤𝐸𝐸𝐸𝐸𝐸𝐸𝐸𝐸𝑢𝑢𝑝𝑝𝑓𝑓𝐸𝐸𝑖𝑖𝑓𝑓𝑛𝑛𝐸𝐸𝐸 𝛼𝛼∙𝜂𝜂𝑖𝑖=1)， (16) 392
The final composite feature representation emerges from the synergistic integration of two distinct sources: the output 𝛤𝛤𝐶𝐶𝐶𝐶𝐶𝐶𝐶𝐶−𝐸𝐸, which fuses the 393 MASA-Optimizer extractor with the MPC-Poisson constraints, and the cumulative historical feature pool Σ𝛤𝛤𝐸𝐸𝐸𝐸𝐸𝐸𝐸𝐸𝑢𝑢𝑝𝑝𝑓𝑓𝐸𝐸𝑖𝑖𝑓𝑓𝑛𝑛𝐸𝐸𝐸 𝛼𝛼∙𝜂𝜂𝑖𝑖=1, which is selectively drawn 394 from the experience replay memory. By jointly leveraging the current optimization pathway and strategically replayed past information, this 395 formulation enables the model to construct a temporally robust and structurally consistent representation. In the early stage of optimization, the model 396 lacks sufficient familiarity with the task and environment. Moreover, the introduction of MPC-Poisson imposes a stronger constraint on the loss 397 function, which results in a high loss value and considerable volatility in the forgetting factor 𝛼𝛼, potentially destabilizing the training process. To 398 mitigate this, a simulated annealing strategy is initially applied to avoid premature convergence to local optima. The initial temperature is set to 100°C, 399 with a minimum temperature of 1°C and a cooling rate of 0.95. The initial value of the forgetting factor 𝛼𝛼 is set to 0.1. In each iteration, a new 400 candidate value of 𝛼𝛼 is generated as follows: 401
𝛼𝛼′=𝛼𝛼+Δ𝛼𝛼, Δ𝛼𝛼~𝑈𝑈𝑈𝑈𝑈𝑈𝑠𝑠𝑠𝑠𝑈𝑈𝑈 𝑈𝑈𝑈 (−0.1,0.1)， (17) 402
If the candidate value 𝛼𝛼′ falls outside the valid range [0,1], it is directly set to 1. If the new value leads to a reduction in the overall loss, it is 403 immediately accepted; otherwise, it may still be accepted with a certain probability to enable global exploration, consistent with the Metropolis 404 criterion. The temperature is gradually reduced at each iteration by multiplying it with the cooling rate. As the temperature decreases and the training 405 enters the mid-to-late stage, the model's understanding of the task improves. At this point, a reinforcement learning mechanism is introduced to fine-406 tune 𝛼𝛼, enhancing the model's long-term performance. Specifically, MASA-Optimizer employs a Q-learning algorithm to construct a discretized 407 state-action mapping framework. The state space is defined based on the model’s total loss 𝐿𝐿𝑡𝑡𝑡𝑡𝑡𝑡𝑡𝑡𝑡 , which is discretized into 20 levels through linear 408 quantization, enabling dynamic monitoring of model performance. A 20×3 Q-table matrix is maintained to store the long-term expected value for 409 each state-action pair. This matrix is initialized with zeros, with a learning rate 𝑙𝑙𝑎 =0.1 for parameter updates and a discount factor 𝛾𝛾=0.9 to balance 410
Input LayerHidden LayersOutput Layer(batch_size×height×width,2)Swish′++++SwishSwish
12
future rewards.The action selection follows an ε-greedy strategy, where the exploration rate 𝜙𝜙=0.9 allows for a 90% probability of randomly 411 exploring new actions, while a 10% probability is assigned to exploiting the best-known action with the highest Q-value for the current state. After 412 each action execution, the system evaluates the loss variation rate 𝛿𝛿𝑡𝑡 to capture performance fluctuation: If 𝛿𝛿𝑡𝑡>0, indicating an increase in training 413 loss, the integration of historical features should be suppressed. If 𝛿𝛿𝑡𝑡<0, suggesting a loss reduction, the replay of past features can be increased.This 414 feedback signal drives the generation of an adaptive step size 𝑆𝑆𝑡𝑡, computed using a non-linear compression function: 415
𝑆𝑆𝑡𝑡=𝑒 10|𝛿𝛿𝑡𝑡|−𝑒 −10|𝛿𝛿𝑡𝑡|𝑒 10|𝛿𝛿𝑡𝑡|+𝑒 −10|𝛿𝛿𝑡𝑡|， (18) 416
The adaptive step size 𝑆𝑆𝑡𝑡 is constrained such that its maximum value is capped at 1, while its value asymptotically approaches zero under stable 417 learning conditions. The update rule of the forgetting factor α is deeply coupled with the magnitude of the step size： 418
Δ𝛼𝛼=􀵝−𝑆𝑆𝑡𝑡 Θ=0+𝑆𝑆𝑡𝑡 Θ=10 Θ=2， (19) 419
When Θ=0, the increment (−𝑆𝑆𝑡𝑡) decreases 𝛼𝛼; when Θ=1, the increment (+𝑆𝑆𝑡𝑡) increases 𝛼𝛼; and when Θ=2, the value of 𝛼𝛼 remains 420 unchanged. The updated value of 𝛼𝛼 is computed as: 421
𝑎𝑎𝑡𝑡+1=𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑈𝑈𝑐𝑐𝑗𝑗[0,1](𝛼𝛼𝑡𝑡+Δ𝛼𝛼(𝛼𝛼𝑡𝑡,𝛿𝛿𝑡𝑡))， (20) 422
The value of 𝛼𝛼 is constrained within the interval [0,1] to ensure numerical validity. Simultaneously, the reward mechanism encodes the loss 423 variation as an immediate reward. The Q-table is iteratively updated using the Bayesian equation, enabling temporal propagation of state–action 424 values and guiding the agent toward optimal strategies during the mid-training phase. However, in the later stages, the Q-table updates rely on localized 425 feedback from current state–action pairs, making it difficult to escape previously explored regions and achieve global optimization. To address this, 426 a global search is initiated via genetic algorithms, incorporating minor mutation and crossover operations to fine-tune the neighborhood around the 427 current solution and improve generalization. 428
At the onset of the final optimization stage, a set of initial candidate solutions (individuals) is sampled from the stable region of the population 429 evolved by reinforcement learning. Each individual encodes a single floating-point value 𝛼𝛼, representing the forgetting factor. The initial population 430 is defined as: 431
𝜌𝜌0={𝑎𝑎𝑖𝑖~𝑈𝑈(𝑎𝑎𝑚𝑚𝑚𝑚𝑚𝑚𝑖𝑖𝑛𝑛,𝑎𝑎𝑚𝑚𝑡𝑡𝑢𝑢)}， (21) 432
Specifically, N=20 denotes the population size, and U(𝑎𝑎𝑚𝑚𝑚𝑚𝑚𝑚𝑖𝑖𝑛𝑛,𝑎𝑎𝑚𝑚𝑚 ) represents a uniform distribution over the interval [𝑎𝑎𝑚𝑚𝑖𝑖𝑛𝑛,𝑎𝑎𝑚𝑚𝑚 ] . This 433 initialization strategy inherits the optimization outcomes from the RL phase and continues the refinement process to avoid premature convergence to 434 suboptimal local solutions.Each individual is evaluated over five iterations, and the average loss is used to determine its fitness. Individuals with 435 lower average losses are selected as parents. To enhance population diversity, offspring are generated through crossover operations. In this study, we 436 adopt an arithmetic crossover approach, where two offspring are produced by averaging the values of two parent individuals: 437
𝑎𝑎𝑐 ℎ𝑖𝑖𝑖 𝑖𝑖=𝑎 𝑝𝑝𝑝𝑝𝑝𝑝𝑝𝑝𝑝𝑝𝑝 1+𝑎 𝑝𝑝𝑝𝑝𝑝𝑝𝑝𝑝𝑝𝑝𝑝 22+𝜀𝜀𝑖𝑖，𝑖 ∈[1,2]， (22) 438
Here, 𝜀𝜀1, 𝜀𝜀2~U(-0.1,0.1) represent random perturbations used to increase the diversity of the population. The arithmetic crossover effectively 439 integrates advantageous traits from the parent individuals, while the random perturbations generate novel gene combinations, thereby enhancing the 440 global search capability of the algorithm. To further prevent the algorithm from being trapped in local optima, we introduce a mutation operation, 441 which perturbs an individual by adding a small random value. This operation is formally defined as: 442
𝑎𝑎′=𝑎𝑎+ 𝛷𝛷∙𝜉𝜉,𝜉𝜉𝜉𝜉𝜉𝜉(0~1)， (23) 443
Here, 𝑎𝑎 denotes the original individual, Φ=0.1 represents the mutation intensity, and 𝜉𝜉 is a random perturbation sampled from a standard 444 normal distribution. If the mutated individual 𝑎𝑎′ exceeds the boundary of the interval [0,1], it is truncated to remain within valid limits. In each 445 iteration, the population is updated through a sequence of selection, crossover, and mutation operations. Once the maximum number of iterations is 446 reached, the iteration counter is reset, and the individual with the highest fitness score is selected as the optimal solution. 447
After undergoing the three-stage optimization in the training phase, the inherited factor 𝛼𝛼 converges towards its optimal value. At this point, 448 the model has effectively adapted to the dynamic variations in species and phenology, reaching an optimal balance between the corresponding feature 449 weights. The integration of the MASA-Optimizer significantly enhances the model's ability to fuse heterogeneous features, even under conditions of 450 substantial inter-species and phenological variability and imbalanced sample distributions. This ultimately improves the robustness and generalization 451 capability of the segmentation framework. 452
13
453
Fig. 5 Overview of the MASA-Optimizer framework for dynamic adaptation in continual learning. The module integrates simulated 454 annealing, reinforcement learning, and genetic search across three optimization stages to dynamically adjust the forgetting factor 𝜶𝜶, thereby 455 maintaining a balance between historical and novel feature representations. Experience replay and adaptive updates enable robust fusion of 456 multi-temporal canopy features under species and phenological variability. 457
2.7 Canopy-Level Analysis and Statistical Assessment within the Framework 458
In this study, we propose a Crown Index Calculation Method (SCVI) to compute vegetation indices by integrating vegetation index analysis with 459 instance-level segmentation-based counting. This method effectively addresses the limitations of traditional Spectral Index Calculation (SIC) methods 460 in handling background interference such as soil and non-target elements. Taking the Excess Green Index (ExG) (N. Soontranon et al., 2014) as an 461 example, the SCVI method first utilizes the instance segmentation mask to identify the precise location of each individual canopy within the image 462 and maps it back to the corresponding region in the original image. Next, the RGB channels of each pixel within the canopy region are extracted and 463 normalized to the [0,1] range. Based on the normalized RGB values, the ExG for each pixel is computed using the following equation: 464
𝐸𝐸𝐸 𝐸𝐸=2×𝐺 𝑛𝑛𝑛𝑛𝑛𝑛𝑛𝑛𝑡𝑡𝐸𝐸𝑚𝑚−𝑅𝑅𝑛𝑛𝑡𝑡𝐸𝐸𝑚𝑚−𝐵𝐵𝑛𝑛𝑡𝑡𝐸𝐸𝑚𝑚， (24) 465
Here, 𝑅𝑅𝑛𝑛𝑛𝑛𝑛𝑛𝑛𝑛𝑡𝑡𝐸𝐸𝑚𝑚、𝐺 𝑛𝑛𝑛𝑛𝑛𝑛𝑛𝑛𝑡𝑡𝐸𝐸𝑚𝑚、𝐵𝐵𝑛𝑛𝑛𝑛𝑛𝑛𝑛𝑛𝑡𝑡𝐸𝐸𝑚𝑚 represent the normalized values of the red, green, and blue channels, respectively. Subsequently, the minimum 466 and maximum ExG values within the corresponding canopy region are computed, denoted as 𝑒𝑒𝑒 𝑒𝑒_𝐼𝐼𝑠𝑠𝑠𝑠 and 𝑒𝑒𝑒 𝑒𝑒_𝐼𝐼𝑎𝑎𝑥𝑥, respectively. Based on these 467 values, each pixel's standardized vegetation index is calculated using the following normalization formula: 468
𝑒𝑒𝑒 𝑒𝑒_𝑛𝑛𝑛𝑛𝑛𝑛𝑛𝑛𝑠𝑠𝑐𝑐𝑈𝑈𝐼𝐼[𝑖 ,𝑗𝑗]=𝑒 𝑒𝑒[𝑖𝑖,𝑗 ]−𝑒 𝑒𝑒_𝑚𝑚𝑖𝑖𝑛𝑛𝑒 𝑒𝑒_𝑚𝑚𝑡𝑡𝑢𝑢−𝑒 𝑒𝑒_𝑚𝑚𝑖𝑖𝑛𝑛+𝜀𝜀， (25) 469
Here, (𝑖 ,𝑗𝑗) denotes the pixel location within the two-dimensional array, and 𝜀𝜀 is a small constant introduced to prevent division by zero. 470 Through this process, we obtain a series of output results as illustrated in Fig. 2 (E), which enable the construction of high-resolution vegetation index 471 maps for each individual tree crown. Each pixel accurately reflects its local growth condition. Based on the computed 𝑒𝑒𝑒 𝑒𝑒_𝑛𝑛𝑛𝑛𝑛𝑛𝑛𝑛𝑠𝑠𝑐𝑐𝑈𝑈𝐼𝐼[𝑖 ,𝑗𝑗], the system 472 generates a corresponding pixel-level vegetation index heatmap. Subsequently, the overall vegetation index for each tree crown is derived by averaging 473 all pixel-level vegetation index values within its corresponding mask region. During the mask generation process, a unique identifier is assigned to 474 each crown, and its corresponding vegetation index is recorded and stored. Upon completion of the segmentation and index computation, the 475 framework further calculates the mean vegetation index across all crowns and determines the total number of crowns within the area, enabling crown-476
Experience ReplayDepositDepositDepositReplayReplayReplayLossIterationIntermediate stageQ-TableThe Q-table is 20×3 because 20 states discretize ℒ𝑡𝑡𝑡𝑡𝑡𝑡𝑡𝑡𝑙𝑙 ,and 3 actions adjust 𝛼𝛼(increase, decrease, or remain unchanged).ActionΘPolicyTransition𝑄(𝑐𝑐𝑡𝑡+1,Θ𝑡𝑡+1)⟵𝑄(𝑐𝑐𝑡𝑡,Θ𝑡𝑡))EnvironmentReward𝑈𝑈𝑡𝑡+1Initial StateΕ𝑡𝑡Input StateInput StateActionActionRewardRewardThe update of 𝛼𝛼Next StateΕ𝑡𝑡+1Ε𝑡𝑡=𝑠𝑠𝑠𝑠𝑡(𝐿𝐿𝑡𝑡𝑡𝑡𝑡𝑡𝑡𝑡𝑙𝑙×10)Preliminary stageIf reduces the loss, it is accepted directly. However, even if it increases the loss, it still has a high probability of being accepted in the early stages, promoting exploration and allowing suboptimal solutions to prevent premature convergence into local optima.
𝛼𝛼4𝛼𝛼5𝛼𝛼6𝛼𝛼7𝛼𝛼8𝛼𝛼14𝛼𝛼15𝛼𝛼16𝛼𝛼17𝛼𝛼18-0.98-0.95-0.88-0.81-0.83-0.92-0.77-0.85-0.94-0.92𝛼𝛼14𝛼𝛼24𝛼𝛼15𝛼𝛼25𝛼𝛼16𝛼𝛼26𝛼𝛼17𝛼𝛼27𝛼𝛼18𝛼𝛼28𝛼𝛼34𝛼𝛼44𝛼𝛼34𝛼𝛼44𝛼𝛼34𝛼𝛼44𝛼𝛼34𝛼𝛼44𝛼𝛼34𝛼𝛼440.860.750.760.800.790.760.810.750.780.820.760.720.810.850.750.710.860.820.820.93Arithmetic CrossoverUltimate Stage𝛼𝛼4𝛼𝛼5𝛼𝛼6𝛼𝛼7𝛼𝛼8𝛼𝛼14𝛼𝛼15𝛼𝛼16𝛼𝛼17𝛼𝛼18𝛼𝛼18𝛼𝛼28𝛼𝛼38𝛼𝛼48𝛼𝛼14𝛼𝛼15𝛼𝛼16𝛼𝛼17𝛼𝛼24𝛼𝛼26𝛼𝛼27𝛼𝛼34𝛼𝛼36𝛼𝛼37𝛼𝛼44𝛼𝛼45𝛼𝛼46𝛼𝛼47-1.76-1.33-0.92-0.98-0.81-0.92-0.85-1.53-1.33𝛼𝛼1𝛼𝛼3𝛼𝛼5𝛼𝛼6𝛼𝛼7𝛼𝛼8𝛼𝛼9𝛼𝛼10-1.25-1.31-0.94-0.95-0.83-0.90-0.88-1.03-1.22𝛼𝛼2𝛼𝛼4𝛼𝛼11𝛼𝛼12𝛼𝛼13𝛼𝛼14𝛼𝛼15𝛼𝛼16𝛼𝛼17𝛼𝛼18𝛼𝛼19𝛼𝛼20-1.25-1.13Step1Step2Step3Step4Step5Superior genesMutation
14
level statistics and analysis at regional, individual, and pixel scales. 477
3. Experiments 478
3.1 Experimental Configuration 479
In this study, to comprehensively evaluate the applicability and advantages of the CSAF framework for ecological monitoring, we conducted 480 systematic experiments on forest remote sensing imagery across diverse species, phenological stages, and spatiotemporal scales. Specifically, we 481 performed comparative analyses between CSAF and eleven mainstream instance segmentation models implemented within the MMDetection 482 framework(Chen et al., 2019), including Cascade Mask R-CNN (CMR) (Cai & Vasconcelos, 2021), Swin Transformer (ST) (Liu et al., 2021), Hybrid 483 Task Cascade (HTC) (Chen et al.), YOLOv5 (YV)(Zhang et al., 2022), Yolact (YL) (Bolya et al., 2019), QueryInst (QI) (Fang et al., 2021), Solov2 484 (SL) (Wang et al.), and Mask R-CNN (MR)(He et al.).In addition, we incorporated three advanced models specifically designed for individual tree 485 crown delineation: Detectree2 (DT) (Ball et al.), Tree Crown Delineator (TCD) (Veitch-Michaelis et al.), and MASK_RCNN_TCDD (MRT) (Braga 486 et al.). These models are widely recognized and frequently employed in remote sensing-based tree crown segmentation, providing strong and credible 487 task-specific baselines. Through direct comparison with these domain-oriented models, we aim not only to benchmark the segmentation accuracy of 488 CSAF but also to demonstrate its superior generalization under heterogeneous ecological conditions, including variations in species, phenological 489 stages, and spatiotemporal distributions. This systematic evaluation underscores the potential of CSAF for robust and scalable applications in practical 490 ecological monitoring tasks. 491
All models were trained and evaluated under identical conditions to ensure fairness. The experiments were conducted on a workstation equipped 492 with a 14-core Intel® Xeon® Platinum 8362 CPU (2.80 GHz) and an NVIDIA RTX 3090 GPU, running Ubuntu 18.04.6 LTS, Python 3.7, and 493 PyTorch 1.12.1. Model training used the Adam optimizer with an initial learning rate of 1×10⁻⁴, dynamically adjusted throughout the training process. 494 The batch size was fixed at 2. For stable evaluation, both CSAF and baseline models were assessed using the final 9 checkpoint weights, and reported 495 results correspond to the best performance observed on the test set. All training processes were strictly limited to 1 hour per model on a single GPU. 496
3.2 Evaluation Methods 497
To ensure both objectivity and rigor in performance evaluation, we adopt the Intersection over Union (IoU) metric as a key indicator for 498 quantitatively assessing the model’s effectiveness in tree crown instance segmentation. Intersection over Union (IoU) serves as a widely adopted 499 metric in both object detection and instance segmentation tasks, measuring the spatial consistency between the predicted and ground-truth crown 500 regions by quantifying their area of intersection relative to their union. An elevated IoU value reflects substantial concordance between the predicted 501 and actual crown regions, indicating high segmentation fidelity. Conversely, a lower IoU score reveals limited spatial agreement, suggesting that the 502 predicted boundary poorly aligns with the ground truth, thereby implying reduced segmentation accuracy. Precision quantifies the model’s capability 503 to correctly identify positive instances, defined as the fraction of pixels predicted as crowns that actually correspond to true crown regions. The metric 504 is computed as follows: 505
𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑈𝑈𝑒𝑒𝑐𝑐𝑠𝑠𝑐𝑐𝑠𝑠𝑐𝑐𝑠𝑠 = 𝑇𝑇𝑇 𝑇𝑇𝑇 +𝐹𝐹𝐹 , (26) 506
Within this evaluation framework, True Positives (TP) represent the number of pixels accurately identified as belonging to tree crowns, whereas 507 False Positives (FP) correspond to pixels that are mistakenly classified as crown regions. A higher precision score signifies a lower incidence of false 508 detections, thereby reflecting the model’s reliability and discriminative accuracy in isolating crown areas. In contrast, a lower precision score reflects 509 a higher incidence of false positive predictions, indicating that a substantial portion of the identified crown pixels do not correspond to actual tree 510 crowns, thereby diminishing the credibility and accuracy of the segmentation results. 511
Average Precision (AP) serves as a comprehensive metric by averaging precision scores over a range of Intersection over Union (IoU) thresholds. 512 This index captures the model’s segmentation effectiveness under different overlap conditions and offers a robust summary of performance 513 consistency. The formal definition and calculation method for AP are as follows: 514
𝐴𝐴𝐴 =∫𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑃𝑈𝑈𝑒𝑒𝑐𝑐𝑠𝑠𝑐𝑐𝑠𝑠𝑐𝑐𝑠𝑠(𝑟 )10𝑑𝑑𝑑 , (27) 515
In this context, 𝑟 refers to recall. An elevated Average Precision (AP) value signifies that the model consistently achieves accurate segmentation 516 across a wide range of Intersection over Union (IoU) thresholds, indicating strong general performance. Conversely, a lower AP score reflects limited 517 segmentation reliability under varying IoU conditions, thereby implying weaker overall model efficacy. 518
The metric AP50 denotes the average precision computed under a relatively lenient IoU threshold of 0.5, offering insights into model accuracy 519 when moderate overlap between prediction and ground truth is acceptable. In contrast, AP75 is derived using a stricter IoU threshold of 0.75, thereby 520
15
reflecting the model’s ability to produce highly accurate segmentations with minimal boundary deviation. The metric APm quantifies the model’s 521 segmentation accuracy specifically for medium-sized tree crown instances, thereby providing a focused evaluation of its effectiveness on intermediate-522 scale targets. In contrast, APl measures average precision for larger crowns, offering insight into the model’s ability to delineate extensive canopy 523 structures with high fidelity. 524
To quantitatively assess the counting performance of different models, we adopt the accuracy metric, calculated based on the following expression:525 𝐴𝐴𝐴 𝑢𝑢𝑢𝑢𝑢𝑢𝑢𝑢𝑢𝑢𝑈𝑈𝑎𝑎𝑐𝑐𝑦𝑦=1−|𝐶𝐶𝐸𝐸𝑓𝑓𝑑𝑑𝑖𝑖𝐸𝐸𝑡𝑡𝑓𝑓𝑑𝑑−𝑅𝑅𝑓𝑓𝑡𝑡𝑙𝑙|𝑅𝑅𝑓𝑓𝑡𝑡𝑙𝑙， (28) 526
Here, Predicted refers to the number of tree crowns predicted by the model, while Real represents the manually counted number of tree crowns. 527
3.3 Ablation experiment 528
To investigate the contributions and collaborative mechanisms of each module in CSAF, we designed eight ablation experiments (Table 1). The 529 results indicate that each individual module significantly improves performance. GM-Mamba enhances the model's performance through multi-scale 530 edge enhancement, boosting the AP50 to 72.59% (+2.07%). MASA-Optimizer, by dynamically adapting features, raises the AP50 to 72.81% (+2.29%), 531 while MPC-Poisson, leveraging physical constraints, achieves an AP50 of 72.01% (+1.49%). The combination of modules further strengthens the 532 model's performance. GM-Mamba + MASA-Optimizer achieves an AP50 of 74.43% (+3.91%) in complex contour scenarios, and GM-Mamba + 533 MPC-Poisson elevates the AP75 to 73.89% (+3.37%) in non-target vegetation interference scenarios. The CDPO-E engine (MASA-Optimizer + 534 MPC-Poisson) attains an AP50 of 74.51% (+3.99%) through spatiotemporal collaborative optimization. The complete CSAF model (integration of 535 all three modules) delivers the optimal results, with an AP50 of 76.63% (+6.11%) and an AP75 of 44.11% (+5.93%).Additionally, the visual analysis 536 (Fig. 6) further validates the role of each module. GM-Mamba primarily enhances canopy edges but has limited ability to suppress non-target 537 vegetation interference. MASA-Optimizer adapts well to phenological changes but is constrained in contour extraction. MPC-Poisson effectively 538 reduces non-target vegetation interference but is prone to contour fragmentation. However, when these three modules work in synergy, CSAF excels 539 in canopy extraction, boundary refinement, and long-term adaptability, demonstrating the effectiveness and complementarity of each module. 540
Table 1 Ablation study results of CSAF and its submodules. 541
Schemes
Model
Metrics
GM-Mamba
MASA-Optimizer
MPC-Poisson
AP(%)
AP50(%)
AP75(%)
APm(%)
APl(%)
Schemes1
√
40.63
72.59
39.54
23.82
40.30
Schemes2
√
40.48
72.81
42.96
23.69
42.87
Schemes3
√
39.69
72.01
42.01
24.32
41.85
Schemes4
√
√
40.65
74.43
42.66
24.43
42.7
Schemes5
√
√
40.28
73.89
42.82
24.22
42.62
Schemes6
√
√
40.82
74.51
43.24
24.27
43.01
Schemes7
37.93
70.52
38.18
20.53
39.91
Schemes8
√
√
√
42.28
76.63
44.43
25.61
44.25
542
Original image (a) (b) (c) (d) (e)
GM-Mamba
16
MASA-Optimizer
MPC-Poisson
Baseline
CSAF
Fig. 6 verifies the visual comparison of the effectiveness of the modules, in which (a) interference from non-target vegetation, (b) and (c) 543 blurred crown profiles, and (d) and (e) changes in phenological characteristics exist. 544
3.4 Crown segmentation and counting of a single forest across phenology and species 545
We initially compared the crown segmentation performance of CSAF with eleven advanced instance segmentation models on the BT-Set and 546 RT-Set datasets, selecting the best-performing comparison model based on the experimental results. Furthermore, we conducted long-term counting 547 experiments at various spatial and temporal scales on the RRA-Set and RT-Set datasets, comparing CSAF with the selected optimal model to validate 548 its capability for ecological monitoring across multiple spatiotemporal scales. 549
3.4.1 The effect of single forest crown segmentation across phenology and species 550
In the BT-Set dataset, tree crown contours are relatively distinct and individual trees are sparsely distributed, resulting in consistently high 551 segmentation accuracy across all evaluated models. As shown in Table 2, all methods achieve AP50 scores exceeding 80%, with only minor 552 performance differences observed among leading tree crown segmentation models. These results indicate that under low-density, structurally 553 dispersed ecological conditions, deep learning approaches exhibit strong adaptability and robustness. However, further analysis of the visualization 554 results (see Supplementary Fig. 1) reveals that despite the high quantitative scores, several models still exhibit notable misclassifications under 555 shadow interference. For instance, models such as YL, QI, DT, and MRT tend to mistakenly identify shadowed regions as tree crowns 556 (Supplementary Fig. 1(a)), while the ST model shows strong responses to non-target vegetation (Supplementary Fig. 1(b)). Integrating both 557 quantitative metrics and qualitative observations, it is evident that most mainstream models maintain relatively stable segmentation performance in 558 low-density and spatially dispersed canopy environments. Nevertheless, CSAF demonstrates superior robustness in suppressing shadow-induced 559 errors and mitigating misclassification of non-target vegetation. 560
In the RT-Set dataset, which features higher canopy density, pronounced phenological variation, indistinct crown boundaries, and substantial 561 interference from non-target vegetation pose significant challenges to tree crown segmentation. Under these complex conditions, CSAF exhibits a 562 clear performance advantage. As shown in Table 2, CSAF outperforms all baseline models in terms of AP50, achieving a 5.35% improvement over 563 the DT model and more than a 30% increase compared to the SL model. Moreover, CSAF consistently outperforms all competing methods across 564 other key metrics, including AP, AP75, APm, and APl, further confirming its robustness under complex ecological conditions. Notably, despite leaf-565 off samples accounting for only 4.7% of the training data, CSAF remains capable of accurately segmenting crown structures during the leaf-off period. 566 As illustrated in Supplementary Fig. 1(d), the remaining models predominantly focus on regions with visible foliage and struggle to detect crown 567 contours in defoliated conditions. In the scene depicted in Supplementary Fig. 1(c), which involves strong background interference and blurred 568 crown boundaries, CSAF continues to exhibit outstanding segmentation performance and effective interference suppression. Although certain models 569 (e.g., YL, SL, and MR) can identify interfering vegetation with spectral characteristics similar to the target under specific conditions, they still fail to 570 achieve reliable segmentation when confronted with structurally ambiguous crowns or indistinct target boundaries. Taken together, these results 571 demonstrate that CSAF possesses superior adaptability and robustness in single-stand forest environments characterized by pronounced phenological 572
17
heterogeneity and complex background conditions, further validating its potential for long-term forest monitoring and high-precision individual tree 573 counting. 574
Table 2 Performance Comparison of Segmentation Metrics in a Single-Stand Understory Scenario. 575
Data
Metrics(%)
Model
CMR
SW
HTC
YV
YL
QI
MR
SL
DT
MRT
TCD
CSAF
BT-Set
AP
58.35
43.44
61.34
43.92
42.03
43.01
42.96
42.20
64.57
62.64
63.32
63.94
AP50
93.67
91.90
86.89
86.32
86.42
85.36
92.02
84.82
94.83
92.94
94.10
94.89
AP75
69.97
73.22
37.94
36.65
40.06
37.59
72.38
35.94
75.23
74.87
80.26
80.14
APm
39.50
39.40
31.09
33.01
34.49
35.49
37.56
34.35
40.32
36.92
36.11
41.11
APl
60.16
59.85
44.64
44.11
45.04
43.91
62.98
43.38
66.28
64.12
66.64
65.43
RT-Set
AP
38.10
38.44
30.72
33.93
27.99
28.02
36.92
13.44
37.97
33.67
34.48
42.28
AP50
70.33
70.36
65.61
66.38
61.92
46.27
70.47
47.23
71.28
65.91
65.13
76.63
AP75
41.85
40.30
35.41
37.18
23.70
31.04
36.44
3.04
39.46
35.48
34.91
44.43
APm
24.75
21.94
15.27
27.17
14.87
13.93
22.90
4.58
23.04
19.0
19.30
26.61
APl
42.63
40.60
35.46
33.40
24.37
28.01
38.53
15.19
39.98
35.46
36.82
44.25
3.4.2 Canopy counting ability of a single forest across phenology and species 576
We first conducted counting experiments on the RRT-Set dataset. Although the dataset covers a relatively small area, it spans a full year of image 577 acquisition, capturing significant phenological changes and posing high demands on the model's generalization capability. The original image 578 resolution is 4000 × 3000 pixels, but since the model was trained on 640 × 640 pixel images, directly predicting on the full-sized images would 579 significantly degrade performance. Therefore, we employed a grid-based cropping strategy, dividing the original image into 1000 × 1000 pixel sub-580 images, with a 200-pixel buffer zone around the edges of each sub-image to avoid incomplete segmentation of tree crowns at the image boundaries. 581 After processing each sub-image independently, we stitched them together using a gradient fusion approach. Although some masks may be overwritten 582 due to overlapping, the independent analysis of each sub-image ensures the integrity of individual tree counts. 583
As shown in Fig. 7(A), CSAF exhibits high stability in tree counting tasks across different seasons, achieving an annual average accuracy of 584 91.16%, which is substantially higher than that of the DT model (63.75%). For example, during the defoliation period in February—when spectral 585 characteristics of tree crowns undergo significant changes—CSAF maintains a counting accuracy of 92%, whereas the accuracy of DT drops to 57%, 586 indicating its limited ability to cope with the challenges posed by seasonal phenological variation. These results further confirm that CSAF possesses 587 stronger generalization capabilities and is well adapted to seasonal variability in real-world scenarios. The visualization outcomes (see Supplementary 588 Fig. 2) provide additional evidence supporting this conclusion. CSAF accurately delineates individual tree crown boundaries and achieves instance-589 level segmentation even in densely populated canopy areas, with minimal omission. In contrast, the DT model often produces a single mask covering 590 multiple crowns within localized regions and fails to detect a substantial number of tree crowns, significantly compromising counting accuracy. 591 592
Fig. 7 Panel A illustrates the year-round tree counting results of CSAF and DT on the RRT-Set dataset, while Panel B presents their eight-593
A
B
18
month performance on the RA-Set dataset. 594
In addition, we extended the evaluation scope from small-scale forest plots to the RA-Set dataset, which features a more complex ecosystem and 595 more pronounced external disturbances. This broader evaluation imposes stricter requirements on the model, including its ability to cope with seasonal 596 phenological variation, to extract densely packed and spectrally similar crown boundaries, and to resist interference from non-target vegetation. In 597 this experiment, the image cropping and stitching strategy was kept consistent with that used for the RRT-Set. As shown in Fig. 7(B), CSAF achieves 598 an average counting accuracy of 97.88% on the RA-Set, significantly outperforming the DT model, which reaches 82.33%. Moreover, the full-period 599 results reveal that CSAF maintains consistently high stability throughout the year-long counting task, whereas DT exhibits considerable fluctuations 600 in accuracy, indicating markedly lower stability. Further insights from Fig. 8 reveal that during the defoliation period in March 2024, the DT model 601 is heavily affected by fallen leaves, resulting in misclassification of several non-vegetated areas as tree crowns and the generation of erroneous masks. 602 Moreover, its ability to delineate crown contours under leaf-off conditions is markedly inferior to that of CSAF, frequently producing single masks 603 that span multiple individual crowns. These findings suggest that CSAF offers stronger ecological monitoring capability during the defoliation season. 604 In the samples from December 2023, CSAF further demonstrates its crown delineation capability, achieving nearly complete and accurate 605 segmentation of each individual tree crown, with boundaries closely aligning with the actual crown shapes. In contrast, the DT model not only exhibits 606 a high rate of missed detections but also shows significantly lower accuracy in representing crown morphology compared to CSAF. 607 608 abcdefghi
19
Fig. 8 Two representative image groups from December 2023 and March 2024 were selected for visualization. Panels a, b, and c correspond 609 to the original image, CSAF segmentation result, and DT segmentation result for March 2024, respectively. Panels d, e, and f present a 610 zoomed-in region from the same time point, where the presence of abundant fallen leaves leads to notable mis-segmentation by the CMR 611 model. Panels g, h, and i display the original image, CSAF result, and DT result for December 2023, respectively. 612
3.5 Crown segmentation and counting of mixed forest across species 613
This section presents experiments focused on segmentation and counting tasks within multi-species ecological environments. First, we compare 614 CSAF with eleven advanced instance segmentation methods on the UT-Set dataset to validate its segmentation performance advantage in urban areas 615 with complex species distributions. Next, we conduct cross-species generalization experiments on the CTF-Set dataset, selecting the best-performing 616 comparative model. We then perform a comparative analysis of the counting capabilities of CSAF and the selected model across three different forest 617 regions at varying scales within this dataset, thereby evaluating the generalization ability of CSAF in multi-scale scenarios. 618
3.5.1 Canopy segmentation effect of cross-species urban forest and mixed forest 619
The UT-Set dataset presents several challenges, including small target sizes, complex background interference, and significant feature variability 620 resulting from multiple coexisting species. Despite these complexities, CSAF demonstrates outstanding segmentation performance. As shown in 621 Table 3, CSAF consistently outperforms all competing models across all evaluation metrics. Notably, models specifically designed for tree crown 622 segmentation—such as DT, MRT, and TCD—perform poorly on this dataset. In particular, TCD exhibits substantially lower performance compared 623 to CSAF, highlighting its limited adaptability to diverse crown types and its inadequate ability to delineate small-sized tree crowns. The visualization 624 analysis further confirms the advantage of CSAF in small-object detection. For instance, in the region marked by the red box in the lower left of 625 Supplementary Fig. 3(a), CSAF successfully identifies and accurately segments two extremely small tree crowns, whereas all other models fail to 626 effectively delineate the targets in this area. Additionally, in the region highlighted by the blue box, CSAF is also able to clearly distinguish and 627 segment two crowns with notable spectral differences, while the comparison models exhibit common under-segmentation or even complete omission. 628 CSAF also demonstrates strong robustness in handling interference from non-target vegetation. As shown in Supplementary Fig. 3(b), within the 629 red-boxed region, all comparison models—except DT—exhibit mis-segmentation of non-target vegetation, further highlighting CSAF’s interference 630 suppression capability. In summary, CSAF exhibits notable robustness and adaptability when dealing with complex ecological scenarios involving 631 small objects and high species diversity. 632
To further evaluate the strong generalization capability of CSAF, we conducted a cross-species segmentation experiment on the CTF-Set dataset, 633 which exhibits substantially higher species diversity. This dataset comprises 24 heterogeneous tree species across 12 botanical families, with 634 pronounced differences in crown morphology. In this experiment, we directly applied the model weights trained on the RT-Set without any additional 635 fine-tuning, thereby imposing an even greater challenge to the model’s generalization capacity. As shown by the quantitative results in Table 3 and 636 the visualization analysis in Supplementary Fig. 3(c,d), CSAF maintains superior segmentation performance even when applied to previously unseen 637 mixed-species forest scenarios—outperforming its results on the RT-Set. We hypothesize that this outcome may be attributed to the morphological 638 and spectral heterogeneity among tree crowns in mixed forests, which, in contrast to the uniform characteristics typically observed in monospecific 639 stands, facilitates the model’s ability to distinguish individual crowns and thereby improves segmentation accuracy. Moreover, the continual learning 640 mechanism embedded in the MASA-Optimizer enables CSAF to progressively adapt to diverse crown morphologies and spectral variations during 641 training, resulting in smoother feature representation transitions and enhanced generalization stability. In contrast, the other comparison models exhibit 642 a substantial drop in performance on this dataset, with overall results falling significantly short of those achieved by CSAF. This further substantiates 643 CSAF’s robustness and broad adaptability in cross-species forest segmentation scenarios. 644
Table 3 Performance comparison between CSAF and baseline models on the UT-Set and CTF-Set datasets. 645
Data
Metrics (%)
Model
CMR
SW
HTC
YV
YL
QI
MR
SL
DT
MRT
TCD
CSAF
UT-Set
AP
43.01
40.41
42.33
33.04
31.61
33.69
34.83
27.42
43.41
43.37
32.93
48.31
AP50
74.79
73.79
70.83
69.74
65.41
66.25
68.41
60.81
68.54
67.53
66.09
77.03
AP75
55.80
50.91
48.99
29.93
21.62
29.29
36.24
20.97
51.18
50.60
38.50
59.66
APm
45.58
43.95
44.12
36.94
37.80
39.26
41.70
31.04
49.63
47.86
32.54
51.95
APl
58.44
46.31
58.67
34.03
26.24
40.66
29.61
40.19
58.37
59.18
41.54
63.32
20
CTF-Set
AP
29.3
27.1
22.00
18.3
22.1
13.8
30.2
11.4
31.55
25.65
23.00
35.43
AP50
67.8
59.4
53.5
48.9
53.0
43.8
63.9
39.6
69.67
65.68
60.44
76.71
AP75
24.6
24.3
16.3
11.0
15.3
6.2
24.1
3.1
28.77
24.03
18.68
27.57
APl
29.5
27.3
22.3
18.4
22.2
13.9
30.1
11.4
31.54
28.94
23.00
35.48
3.5.2 Canopy counting generalization performance of mixed forest across species 646
In the crown counting task within mixed-species forests, CSAF maintains consistently high counting accuracy across all three regions, as 647 illustrated in Fig. 10. In contrast, DT exhibits greater variability in counting stability across different areas. For instance, in Region 3, where tree 648 crowns are relatively large, DT’s accuracy drops to 77%, whereas CSAF remains stable at 92%, demonstrating superior robustness. In conjunction 649 with the visualization analysis (Fig. 9), CSAF demonstrates superior segmentation accuracy in areas such as zone1 and zone2, where tree crowns are 650 relatively small. In these regions, CSAF is able to accurately and almost completely segment all tree crowns, with clearly defined boundaries. In 651 contrast, DT exhibits a high rate of under-segmentation, severely compromising the completeness of the counting results. Taken as a whole, CSAF 652 demonstrates consistent generalization ability and monitoring stability across a range of large-scale crown counting scenarios, including both single-653 species and mixed forests, varying spatial extents, and different phenological stages. These results suggest that CSAF can serve as a reliable tool to 654 support high-precision and stable forest monitoring for future fine-scale management applications. 655 656
Fig. 9 Panels a, d, and g represent zone1, zone2, and zone3, respectively. Panels b, e, and h show the segmentation results of DT in zone1, 657
abcdefghi
21
zone2, and zone3, while panels c, f, and i display the corresponding segmentation results of CSAF in the same zones. 658 659
Fig. 10 Tree Crown Counting Results of CSAF and DT Across Three Regions in the Mixed-Species Forest 660
3.6 Extreme event application 661
On September 6, 2024, around 16:20, the island province of Hainan, China, was directly struck by the super typhoon "Mojia," which was a 662 powerful tropical cyclone with wind speeds exceeding 62 m/s and a minimum central pressure of 915 hPa. This typhoon caused significant damage 663 to local areas. We selected the region with the most severe damage to conduct a quantitative study of vegetation dynamics before and after the disaster. 664 The study area is a closed polygon, with the following geographical coordinates (WGS84) of its vertices: (19°32′34.85876″N, 109°29′10.07322″E), 665 (19°32′34.87772″N, 109°29′09.89346″E), (19°32′34.92590″N, 109°29′10.96543″E), (19°32′37.97414″N, 109°29′16.32023″E), (19°32′40.86399″N, 666 109°29′15.27706″E), (19°32′37.71382″N, 109°29′11.47715″E), (19°32′38.04730″N, 109°29′11.92179″E), (19°32′37.22500″N, 109°29′10.20321″E), 667 and (19°32′36.19759″N, 109°29′10.47651″E). 668
To comprehensively assess the vegetation changes before and after the typhoon, as well as the post-disaster recovery rate, we collected and 669 analyzed remote sensing imagery data from 11 time points spanning 2024 and early 2025. The specific dates include: August 9, September 5 (pre-670 disaster), September 9, October 9, October 24, October 31, November 8, November 15, November 28, December 18, and January 24, 2025, and 671 February 6, 2025. 672
To comprehensively assess the effectiveness of vegetation indices derived from RGB imagery, we curated a set of 16 representative visible-673 spectrum indices encompassing brightness amplification, chromatic enhancement, and spectral ratio-based formulations. These indices are: Excess 674 Green Index (ExG), Visible Atmospherically Resistant Index (VARI), Green-Red Vegetation Index (GRVI), Green Leaf Index (GLI), Color Vegetation 675 Index (CVI), Vegetation Extracted Color Index (CIVE), Triangular Green Index (TGI), Vegetation Green Index (VGI), Excess Red Index (ExR), 676 Excess Green minus Excess Red Index (ExGR), Normalized Difference Index (NDI), Modified Green-Red Vegetation Index (MGRVI), Excess Green-677 Red-Blue Difference Index (EGRBDI), Red-Green-Blue Ratio Index (RGBRI), Enhanced Normalized Green-Blue Difference Index (E-NGBDI), and 678 Excess Blue Vegetation Index (ExB)(Hunt et al., 2013; Meyer & Neto, 2008; Motohka et al., 2010; Qi et al., 1994; Rees, 1963; N. Soontranon et al., 679 2014; Vincini et al., 2008; Zeng et al., 2022). The detailed computational expressions for all selected vegetation indices are presented in Supplementary 680 Table 1, offering a concise mathematical overview of their respective formulations. To evaluate how vegetation responds dynamically to post-disaster 681 impacts, we tracked the temporal trajectories of selected vegetation indices, capturing patterns of both degradation and subsequent recovery over 682 time. 683
3.6.1 Counting of affected trees 684
Compared to the counting experiments conducted under stable environmental conditions, we further validated the robustness and accuracy of 685 the CSAF model under post-disaster conditions (Fig. 11). The average accuracy for the entire year reached 92.46%, indicating that the CSAF model 686 not only achieves high-precision counting in stable, well-structured vegetation environments but also maintains strong adaptability in scenarios where 687 the vegetation structure is severely disrupted by a super typhoon. Although there was a slight decrease in accuracy compared to pre-disaster conditions, 688 this minor variation may be attributed to atypical deformations of tree crowns, such as tilting, tearing, or overlapping, caused by the typhoon, which 689 blurred target boundaries and interfered with frame recognition. Additionally, post-typhoon surface background complexity significantly increased 690 due to the presence of non-target disturbances such as fallen branches, exposed soil, and other debris, potentially introducing some degree of false 691
22
vegetation interference. However, it is noteworthy that the model demonstrated stability in the most typical post-disaster disturbance scenarios. For 692 example, after Typhoon "Capricorn" on September 6, when counting was performed on images obtained on September 9, the model's accuracy 693 remained high at 94%, with only an error of 11 trees, fully meeting the accuracy requirements for practical applications. This demonstrates the CSAF 694 model's excellent generalization ability in complex post-disaster scenarios. 695 696
Fig. 11 illustrates the accuracy of the count, where "Real" denotes the actual value, "Pred" represents the model's predicted count, and 697 "Acc" indicates the accuracy. 698
3.6.2 Analysis of Monitoring Conditions Before and After Disasters 699
In the post-typhoon vegetation health assessment, we performed a systematic comparison between the SCVI method and the traditional SIC 700 method, with a focus on examining the differences in their performance in characterizing post-disaster vegetation dynamics. The SCVI method 701 effectively minimizes the interference from non-vegetative factors such as soil background, thereby providing a more accurate representation of the 702 spectral characteristics of the vegetation itself. In contrast, the SIC method does not fully eliminate the influence of background noise, which may 703 lead to deviations in the calculated indices. As shown in Fig. 12, for the ExG index, the value obtained using SCVI was 0.5413, while the SIC method, 704 influenced by background interference, yielded a value of 0.4587, resulting in a difference of 0.0826 and a relative error of 15.26%. In terms of post-705 disaster response sensitivity, SCVI similarly demonstrated a clear advantage. For instance, in the case of the MGRVI index, SCVI detected an increase 706 from 0.7774 to 0.8779, reflecting a rise of 12.9%, while the SIC method showed a slight decrease from 0.9955 to 0.9664, corresponding to a 2.9% 707 reduction. These results suggest that SCVI not only captures subtle variations in vegetation damage more sensitively but also exhibits superior delay 708 response capabilities and parameter stability, significantly enhancing the accuracy of post-disaster ecological assessments and providing a robust 709 foundation for high-quality monitoring. 710
Of particular note is the observation from long-term temporal monitoring, which revealed that, with the exception of indices such as ExR and 711 ExB that reflect the intensity of red and blue light bands, most vegetation indices predominantly based on the green light band exhibited a general 712 "decline followed by recovery" trend under SCVI computation. This pattern aligns with the expected ecological recovery process following a disaster. 713 In contrast, several indices calculated using the SIC method (e.g., VARI, VGI) displayed an anomalous "increase followed by decline" pattern, 714 indicating potential biases in capturing post-disaster, non-steady-state vegetation changes. This phenomenon further confirms the reliability and 715 accuracy of SCVI for monitoring the entire pre-disaster, post-disaster, and recovery phases. 716
Building upon this foundation, we conducted an in-depth analysis of various representative sensitive vegetation indices to provide essential 717 indicator support for the subsequent development of a multi-scale monitoring system encompassing "regional assessment—individual plant 718 positioning—pixel-level analysis." For instance, the ExG index showed a significant decline from 0.4966 to 0.3758 after the typhoon, with a decrease 719 of 0.1208, while the CVI index also demonstrated a downward trend, reflecting reduced chlorophyll content and canopy structure damage. These 720 changes highlight the high sensitivity of these indices to typhoon disturbances. Additionally, the MGRVI and ExB indices exhibited an increase post-721 disaster, with the rise in ExB attributed to a reduction in green light reflection and an enhancement of blue light reflection, while the increase in 722 MGRVI revealed its unique response mechanism to post-disaster spectral structure changes. This prompted us to focus on the temporal evolution of 723 these indices for further exploration. 724
23
725
Fig. 12 compares the vegetation index calculation differences between the SCVI and SIC methods and illustrates their temporal recovery 726 patterns. The fitted trend lines represent the vegetation index values across different time periods, and the R² values indicate the proportion 727 of variance explained by the trend lines, serving as a measure of fit quality. 728
At the regional scale, the dynamic changes in vegetation indices clearly depict the process of ecosystem stability, disturbance, and recovery. In 729 the period preceding the typhoon event, vegetation indices including ExG, CVI, MGRVI, and ExB exhibited relatively consistent values, suggesting 730 a stable canopy condition and a well-functioning forest ecosystem. However, following the typhoon's landfall, these indices underwent pronounced 731 fluctuations. Notably, around October 24, a substantial decline was observed across most indices—excluding ExB—indicating a lagged response of 732 the vegetation to the disturbance. It is postulated that strong winds led to structural damage of the canopy, while the subsequent humid environment 733 may have facilitated the onset of plant diseases, jointly accelerating the physiological degradation of vegetation. By mid-November, a gradual rebound 734 was observed in all indices except ExB, indicating that the forest ecosystem retained a measurable capacity for natural recovery. Following January 735 of the subsequent year, a renewed decline in vegetation indices was observed, largely attributed to seasonal phenological shifts—most notably, 736 extensive leaf shedding from the canopy. This process led to diminished green reflectance and a corresponding rise in ExB values. On the whole, the 737 CSAF monitoring framework, underpinned by the SCVI approach, delivers a nuanced and accurate depiction of ecosystem-level temporal dynamics 738 at regional scales, thereby serving as a valuable tool for post-disaster ecological evaluation and informed forest resource management. 739
In further multi-scale analysis, we focus on three representative tree crowns (a, b, c) and analyze the changes in vegetation indices from the post-740 disaster to recovery phase using individual positioning and pixel-level tracking, particularly during the secondary decline and recovery phase between 741 October 24 and December 18. As shown in Fig. 13 and Table 4, during the early stage (September 9 to October 9), distinct heterogeneity in index 742 changes was observed across the three tree crowns, indicating varying initial responses to the post-disaster environment. During the secondary decline 743 phase (October 24), vegetation indices exhibited significant fluctuations. Specifically, crown a showed a decrease in some indices, crown b 744
24
experienced the greatest decline, while crown c exhibited an unusual increase in certain indices. In the recovery phase, the three crowns displayed 745 different recovery trajectories, revealing distinct mechanisms in resource acquisition and physiological regulation. During the stabilized recovery 746 period (28 November to 18 December), all three tree crowns demonstrated a consistent upward trend in vegetation index values. This improvement 747 was accompanied by a marked decline in red lesion areas, both along crown peripheries and within interior regions, indicating progressive healing 748 and structural restoration following extensive canopy damage. As the phenological transition resumed in February of the following year, vegetation 749 indices exhibited renewed variability, reflecting the dynamic physiological sensitivity of individual crowns and species-specific differences in seasonal 750 acclimation to environmental disturbances. 751 752
Fig. 13 The shaded areas around each trend line represent the 95% confidence intervals of the fitted regressions, reflecting the uncertainty 753 in vegetation index values over time. Narrower intervals indicate greater temporal stability, while wider bands suggest increased fluctuation 754 or higher variability. 755
Table 4 Figures (a), (b), (c), and (d) represent the period from October 24, when a significant decline in the vegetation index was first observed, 756 to November 15, which marks the phase of stabilization and recovery. Figures (e) and (f) depict the period from November 28, when a sharp 757 drop in the vegetation index occurred, through to the recovery phase observed by December 28. For canopy a, the corresponding tree crowns 758 are as follows: October 24 (tree crown 76), October 31 (tree crown 61), November 8 (tree crown 44), November 15 (tree crown 61), November 759 28 (tree crown 17), and December 18 (tree crown 7). For canopy b, the tree crowns are: October 24 (tree crown 79), October 31 (tree crown 760 57), November 8 (tree crown 60), November 15 (tree crown 1), November 28 (tree crown 16), and December 18 (tree crown 111). For canopy 761 c, the tree crowns are: October 24 (tree crown 75), October 31 (tree crown 58), November 8 (tree crown 43), November 15 (tree crown 65), 762 November 28 (tree crown 24), and December 18 (tree crown 2). 763
October 24
October 31
November 8
November 15
November 28
December 18
ExG
CVI
MGRVI
ExB
(a)
(b)
(c)
(d)
(e)
(f)
4. Discussion 764
4.1 Comparative Analysis of Segmentation Errors 765
To ensure the ecological interpretability and practical utility of crown counting results, it is essential to achieve precise delineation of individual 766
25
tree crowns while maintaining effective control over segmentation errors, particularly under ecologically heterogeneous conditions characterized by 767 diverse species compositions and phenological stages. Drawing on established error classification approaches from previous studies(Straker et al., 768 2023; Van den Broeck et al., 2025), we categorized common segmentation errors into three main types: (1) Missed Crowns, (2) Merged of Fragmented 769 Crowns, and (3) Incorrect Vegetation Segmentation. In the preceding analysis, we preliminarily examined how these error types influence overall 770 segmentation performance. However, a systematic assessment of their specific impacts on crown counting accuracy remains lacking. To address this 771 gap, this section focuses on three representative datasets—RRT-Set, RA-Set, and CTF-Set—and conducts a quantitative comparison and detailed 772 interpretation of counting outcomes for both CSAF and DT models in relation to the three identified error categories. This analysis aims to elucidate 773 the models’ differential adaptability and error-control capabilities under ecologically complex conditions. Across the three representative datasets, we 774 observed notable differences in the distribution of segmentation error types under varying ecological and phenological conditions. These differences 775 reveal both the adaptive advantages and structural limitations of CSAF when applied to complex environments. In the RRT-Set, seasonal changes 776 significantly affected the model’s ability to identify crown boundaries. Specifically, the over-segmentation and under-segmentation observed in 777 September and October were closely related to increased canopy density and blurred crown contours(Clark, 2020; Cloutier et al., 2024). Although 778 both CSAF and DT were affected by seasonal variations, CSAF consistently demonstrated more stable error control, particularly in addressing merged 779 and fragmented crown cases. This advantage can be attributed primarily to the structural design of the GM-Mamba module, which enhances long-780 range spatial dependency modeling and boundary delineation. In terms of incorrect vegetation segmentation, the difference between the two models 781 was relatively minor. This may be due to the spectral and morphological similarity between interfering vegetation and target crowns, which limits the 782 effectiveness of the MPC-Poisson module’s morphological regularization. These findings are also consistent with previous studies highlighting 783 confusion between shrub and tree boundaries(Gui et al., 2025; Pires et al., 2022). In the RA-Set dataset, the structural irregularity of non-target 784 vegetation provides clearer geometric cues for the MPC-Poisson module, enabling CSAF to outperform the baseline model DT in suppressing 785 background interference. This result further confirms the effectiveness of the module’s boundary constraint mechanism when dealing with complex 786 background structures. However, in the cross-species evaluation on the CTF-Set dataset, the distribution of segmentation errors exhibits distinct 787 patterns. In such heterogeneous forest environments, model errors primarily stem from insufficient adaptation to inter-species morphological 788 variability and misclassification of non-crown regions, such as lake margins or exposed rocky surfaces. Specifically, the DT model frequently 789 misidentified rock formations as tree crowns in Zone1 and Zone2, while CSAF occasionally misclassified image edges or water bodies as vegetation. 790 These misclassifications reflect a potential side effect of the model’s strong generalization capacity under such conditions. Although the MASA-791 Optimizer module improves cross-domain adaptability in multi-species environments, its continual learning mechanism may occasionally lead to 792 incorrect crown identification due to imbalanced retention of previously learned features and integration of new ones. This observation highlights the 793 need to incorporate explicit suppression strategies for low-confidence regions in order to maintain boundary precision without compromising the 794 model's adaptive flexibility. It is worth noting that although Zone3 covers the smallest spatial extent, its internal crown distribution is highly disordered 795 and characterized by pronounced canopy structural heterogeneity, posing greater challenges for accurate individual crown delineation and counting. 796 This observation further underscores the influence of regional structural complexity on segmentation accuracy(Liu et al., 2025). While CSAF still 797 outperforms DT in overall error control and demonstrates relatively higher stability, it exhibits certain limitations in adapting to such structurally 798 chaotic canopy patterns. This highlights the necessity of incorporating more hierarchical spatial-context modeling mechanisms in future work to 799 enhance the model’s ability to interpret regions with extreme structural heterogeneity. 800 801
26
Fig. 14 Segmentation error types across (left) RRT-Set and (middle) RA-Set phenological months, and (right) CTF-Set ecological zones, 802 comparing CSAF and DT models. Errors are categorized into Missed Crowns, Merged or Fragmented Crowns, and Incorrect Vegetation 803 Segmentation. 804
4.2 Performance Analysis Across Temporal, Spatial, Ecological, and Imagery Domains 805
Our findings demonstrate that CSAF exhibits superior stability and generalization capabilities compared to established tree crown segmentation 806 models such as DT, MRT, and TCD, across multiple spatial and temporal scales, diverse ecological types, and varying imaging modalities. While 807 these baseline models achieve high performance on structurally simple datasets such as BT-Set (see Table 2), their effectiveness declines substantially 808 on more complex and heterogeneous datasets such as RT-Set, UT-Set, and CTF-Set (see Table 3). This performance gap is primarily attributed to the 809 CSAF framework’s explicit modeling of phenological and species-specific feature dynamics, its ability to capture long-range spatial dependencies, 810 and the integration of physically grounded constraints on crown morphology. In contrast, models such as DT, MRT, and TCD lack mechanisms for 811 perceiving complex ecological structures and phenological variability by design. As a result, they tend to exhibit notable performance degradation 812 under increased environmental heterogeneity, particularly in cross-seasonal and structurally diverse scenarios. However, in ecological remote sensing 813 applications, the capacity of a model to maintain stable performance across multiple temporal scales, spatial regions, ecological environments, and 814 imaging modalities is a critical determinant of its suitability for large-scale forest monitoring deployment(Kwon et al., 2023; Parshakov et al., 2025; 815 Wang et al., 2025). The increasing emphasis on cross-domain generalization in remote sensing research further underscores this need (Ma et al., 2024; 816 Mohammadi et al., 2024). Future model development should therefore place greater focus on enhancing adaptability to diverse forest types, 817 phenological stages, and disturbance conditions, and on reinforcing robustness under ecologically heterogeneous scenarios. Such advancements will 818 facilitate stable deployment of remote sensing models across regions and time periods, supporting the demand for high-precision forest monitoring 819 within complex ecosystems. 820
4.3 Advantages of the SCVI Method in Post-Disaster Remote Sensing Monitoring 821
Our findings indicate that conventional spectral index computation methods (SIC) often fail in post-disaster remote sensing applications. This 822 limitation does not primarily stem from the mathematical formulation of the indices themselves, but rather from their inability to effectively separate 823 tree crowns from background elements. Traditional approaches typically calculate vegetation indices over entire images or coarse spatial regions, 824 making them highly susceptible to background interference from exposed soil, fallen branches, and other non-vegetative components. Such 825 interference introduces significant spectral contamination and distorts the temporal response patterns of vegetation indices (Lucas et al., 2007; Lugassi 826 et al., 2010; Ren et al., 2018). This phenomenon is corroborated by our experimental results (see Fig. 12), particularly for certain indices based on 827 green spectral bands. During the early disturbance phase, SIC methods often exhibit an anomalous "increase-then-decline" trend, which deviates 828 substantially from the physiological trajectory of actual vegetation degradation. In contrast, the SCVI approach computes vegetation indices based 829 on instance-level segmentation outputs and incorporates an effective mechanism for suppressing non-target background interference. As a result, it 830 substantially enhances the sensitivity and reliability of vegetation indices in capturing true ecological changes. Specifically, for indices relying on 831 green spectral bands, SCVI exhibits a natural degradation–recovery curve characterized by an initial decline followed by gradual restoration. In 832 contrast, for indices based on red or red-edge bands, SCVI accurately captures a dynamic trajectory of early increase followed by decline, both of 833 which reflect ecologically consistent patterns(Abdelrahim & Jin, 2025). From a methodological perspective, these findings highlight the broad 834 potential of SCVI's instance-guided vegetation index computation framework in addressing the challenges of complex remote sensing 835 environments.Particularly in tasks such as post-disturbance ecological monitoring, phenological response detection, and mixed vegetation 836 identification, SCVI leverages a three-level structure encompassing regional assessment, individual tree localization, and pixel-level analysis. This 837 multi-tiered design enables more accurate representation of vegetation physiological states and facilitates the construction of an integrated ecological 838 monitoring system that bridges macro- to micro-scale observations. 839
4.4 Potential Applications and Methodological Implications 840
Owing to its stable performance across multiple phenological stages, diverse forest types, and extreme post-disaster conditions, the CSAF 841 framework demonstrates strong potential for broader application. In the future, it can be widely employed in critical tasks such as forest resource 842 surveys, post-disaster ecological damage assessment, and stand health monitoring, providing valuable support for forest management and policy-843 making from regional to national scales(Grieger et al., 2025; Sani-Mohammed et al., 2022). In addition, when integrated with UAV platforms or high-844 resolution satellite imagery, CSAF holds promise for enabling low-cost, high-frequency crown-level monitoring, particularly suitable for dynamic 845
27
tracking in remote or inaccessible areas. 846
It is worth noting that the MASA-Optimizer module integrated into CSAF facilitates adaptive modeling across diverse species and phenological 847 stages through a continual learning mechanism. This design endows the model with enhanced robustness and generalization in long-term remote 848 sensing applications. Even under conditions with extremely limited samples from defoliated periods, the module incrementally updates the model’s 849 representation and memory, thereby maintaining the stability and accuracy of segmentation outcomes. It is important to highlight that the value of 850 continual learning strategies extends beyond forest canopy segmentation tasks and has been widely adopted in cross-domain applications, such as 851 medical image diagnosis, to address domain shift issues(Perkonigg et al., 2021). In this context, the adaptive capacity demonstrated by the MASA-852 Optimizer provides a viable solution for modeling seasonal heterogeneity in ecological remote sensing. Moreover, it offers valuable insights into 853 cross-distribution generalization in other heterogeneous scenarios. 854
4.5 Possible improvements 855
Despite the impressive performance demonstrated by CSAF across diverse testing scenarios, several limitations remain. First, although the 856 integration of PINN and the use of diffusion equation–based morphological constraints have significantly improved the model’s capacity to suppress 857 irregularly shaped vegetation such as grasslands, it still exhibits difficulty in distinguishing and suppressing non-target vegetation that exhibits crown-858 like structures. In these cases, the model’s segmentation accuracy remains suboptimal. Second, CSAF encounters challenges in achieving consistent, 859 long-term monitoring of individual tree crowns in densely vegetated areas. Variations in illumination conditions and temporal changes in crown 860 morphology may result in inconsistencies in the delineation of the same tree crown across different time points, leading to temporal fluctuations in 861 segmentation outcomes. Addressing these challenges will be a key objective of our future work. Specifically, we plan to develop a more robust 862 framework capable of ensuring stable, long-term crown-level tracking. Moreover, enhancing the model’s ability to suppress morphologically similar 863 non-target vegetation and further improving its overall segmentation accuracy will remain central to our ongoing research efforts. 864
5. Conclusion 865
Forest ecosystems are increasingly threatened by ecological disturbances and extreme climatic events, particularly in tropical regions where 866 vegetation dynamics are highly sensitive to seasonal fluctuations and storm-related impacts. Accurately monitoring such forests remains a substantial 867 challenge due to their complex canopy structures and continuous temporal variability. In this study, we propose the Canopy Segmentation and Analysis 868 Framework (CSAF), a modular deep learning system that integrates structural priors, adaptive learning strategies, and physical constraint mechanisms. 869 This framework significantly enhances the accuracy and robustness of individual tree crown segmentation and analysis. By integrating the GM-870 Mamba for boundary enhancement, the MASA-Optimizer for continual learning, and the MPC-Poisson module for morphological regularization, 871 CSAF effectively addresses the key limitations of existing instance segmentation methods when applied to complex forest environments. 872
To enable more reliable canopy monitoring under extreme weather conditions, this study introduces a crown-aware vegetation index computation 873 method, SCVI. By suppressing background interference such as exposed soil and fallen leaves, SCVI enhances the spectral signal fidelity and ensures 874 consistent temporal monitoring performance even in post-disturbance environments. Empirical studies conducted across four ecologically 875 heterogeneous datasets demonstrate that CSAF achieves superior performance in both tree crown segmentation and counting tasks, while maintaining 876 stable generalization across diverse ecological and phenological conditions. Notably, in severely disturbed forest regions, the framework enables 877 accurate assessment of damage severity and supports multi-scale ecological interpretation, ranging from regional to individual-tree and pixel levels. 878
The findings indicate that the integration of structure-aware segmentation mechanisms with crown-level spectral quantification establishes a 879 more resilient and scalable solution for forest monitoring. This study highlights the pressing need to address the uncertainties introduced by temporal 880 and structural variability in real-world ecosystems through the integration of remote sensing data, task-specific structural priors, and continual learning 881 strategies. By leveraging high-resolution UAV imagery, the proposed system offers a novel approach for long-term ecological monitoring, particularly 882 suited for forest regions that remain observationally underrepresented or are undergoing rapid transformation. 883
CRediT authorship contribution statement 884
Jiangquan Zeng: Conceptualization, Methodology, Validation, Visualization, Writing–original draft; Mingjie Lv: Writing–review & editing, 885 Supervision, Visualization; Guoxiong Zhou: Methodology, Writing–review & editing, Supervision, Investigation; Xiangjun Wang: Formal analysis, 886 Data curation, Investigation; Jinquan Chao: Writing–review & editing, Supervision, Investigation; Yang Liu: Writing–review & editing, Supervision; 887 Huaiqing Zhang: Writing – review & editing, Supervision; Mingfang He: Writing – review & editing, Supervision; Yongfei Xue: Writing – review & 888 editing, Supervision; Jiale Zhu: Writing–review & editing; Bangqian Chen: Writing–review & editing. The authors used OpenAI’s ChatGPT-4o to 889
28
assist with language refinement during manuscript preparation. 890
Declaration of competing interest 891
The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the 892 work reported in this paper. 893
Acknowledgements 894
We would like to express our sincere gratitude for the financial support provided by the following funding bodies: Central Public-interest 895 Scientific Institution Basal Research Fund (grant number 1630032022007); Hainan Provincial Natural Science Foundation of China (grant number 896 324MS087); National Key R & D Program of China (grant number 2019YFD1000500); Opening Project Fund of Key Laboratory of Biology and 897 Genetic Resources of Rubber Tree, Ministry of Agriculture and Rural Affairs, P. R. China (grant number RRI-KLOF202305); National Natural Science 898 Foundation of China (grant number 32271877). We also extend our deepest thanks to the team members, collaborators, and advisers who contributed 899 to this project. 900
AppendixA. Supplementary data 901
Original
image
CMR
SW
HTC
YV
29
YL
QL
SL
MR
DT
MRT
TCD
30
CSAF
Supplementary Fig. 1 Visual comparison results for the single-species forest experiment. The second to fifth columns correspond to panels 902 (a), (b), (c), and (d), respectively. 903
AppendixB. Supplementary data 904
A
B
31
C
Supplementary Fig. 2 Panels A, B, and C correspond to the original imagery, the segmentation results produced by CSAF, and those generated 905 by DT, respectively. Each sub-panel within A–C represents monthly samples from January to December. 906
AppendixC. Supplementary data 907
Original
image
CMR
SW
HTC
YV
32
YL
QI
SL
MR
DT
MRT
TCD
33
CSAF
Supplementary Fig. 3 Visual comparison results for the mixed forest experiment. The second to fifth columns correspond to panels (a), (b), 908 (c), and (d), respectively. 909
AppendixD. Supplementary data 910
Let 𝑅𝑅 , 𝐺 , and 𝐵𝐵 denote the reflectance values of the Red, Green, and Blue channels, respectively. The following table summarizes the 911 mathematical expressions of the 16 vegetation indices used in this study. 912
Supplementary Table 1 Vegetation Indices and Their Formulas 913
Index Name
Abbreviation
Formula
Excess Green Index
ExG
𝐸𝐸𝐸 𝐸𝐸=2𝐺 −𝑅𝑅−𝐵𝐵
Visible Atmospherically Resistant Index
VARI
𝑉𝑉𝑉𝑉𝑉𝑉𝑉𝑉𝐴𝐴𝑅𝑅𝐼𝐼= 𝐺 −𝑅𝑅𝐺 +𝑅𝑅−𝐵𝐵
Green-Red Vegetation Index
GRVI
𝐺𝐺𝐺𝐺𝐺𝐺𝐺𝐺
𝐸𝐸𝑅𝑅𝑉𝑉𝐼𝐼
=
𝐺 −𝑅𝑅𝐺 +𝑅𝑅
Green Leaf Index
GLI
𝐺𝐺𝐺𝐺𝐺𝐺
𝐸𝐸𝐿𝐿𝐼𝐼
= 2𝐺 −𝑅𝑅−𝐵𝐵2𝐺 +𝑅𝑅+𝐵𝐵
Color Vegetation Index
CVI
𝐶𝐶𝐶𝐶𝐶𝐶𝑉𝑉𝐼𝐼= 𝐺𝐺2𝑅𝑅∙𝐵𝐵
Vegetation Extracted Color Index
CIVE
𝐶𝐶𝐶𝐶𝐶𝐶𝐶𝐶𝐼𝐼𝑉𝑉𝐸𝐸 = 0.441𝑅𝑅−0.881𝐺 +0.385𝐵𝐵+18.787
Triangular Greenness Index
TGI
𝑇𝑇𝑇 = −0.5∙[190(𝑅𝑅−𝐺 )−120(𝑅𝑅−𝐵𝐵)]
Vegetation Greenness Index
VGI
𝑉𝑉𝑉𝑉𝑉𝑉𝐸𝐸𝐼𝐼= 𝐺 𝑅𝑅+𝐺 +𝐵𝐵
Excess Red Index
ExR
𝐸𝐸𝐸𝐸𝐸𝐸𝑥𝑥𝑅𝑅=1.4𝑅𝑅−𝐺
Excess Green minus Excess Red
ExGR
𝐸𝐸𝐸 𝐸𝐸𝐸 =3𝐺 −2.4𝑅𝑅−𝐵𝐵
Normalized Difference Index
NDI
𝑁𝑁𝑁𝑁𝑁𝑁
𝜉𝜉𝐷𝐷𝐼𝐼
=
𝐺 −𝐵𝐵𝐺 +𝐵𝐵
Modified Green-Red Vegetation Index
MGRVI
𝑀𝑀𝑀𝑀𝑀𝑀𝑀𝑀𝑀𝑀𝐸𝐸𝑅𝑅𝑉𝑉𝐼𝐼= 𝐺 2−𝑅𝑅2𝐺 2+𝑅𝑅2
Excess Green-Red-Blue Difference Index
EGRBDI
𝐸𝐸𝐸𝐸𝐸𝐸𝐸𝐸𝐸𝐸𝐸𝐸𝑅𝑅𝐵𝐵𝐷𝐷𝐼𝐼=2𝐺 −𝑅𝑅−𝐵𝐵𝑅𝑅+𝐺 +𝐵𝐵
Red-Green-Blue Ratio Index
RGBRI
𝑅𝑅𝑅𝑅𝑅𝑅𝐸𝐸𝐵𝐵𝑅𝑅𝑅 =𝑅𝑅𝐺 +𝐵𝐵
Enhanced Normalized Green-Blue Difference Index
E-NGBDI
𝐸𝐸−𝑁𝑁𝑁𝑁𝑁𝑁𝑁𝑁𝑁𝑁𝜉𝜉𝐸𝐸𝐵𝐵𝐷𝐷𝐼𝐼=𝐺 −𝐵𝐵𝐼𝐼𝑎𝑎𝑥𝑥(𝐺 ,𝐵𝐵)
Excess Blue Index
ExB
𝐸𝐸𝐸𝐸𝐸𝐸𝑥𝑥𝐵𝐵=2𝐵𝐵−𝑅𝑅−𝐺
Data availability 914
All data supporting the findings of this study will be made publicly available upon publication. The relevant datasets will be depos915 ited in public repositories in accordance with the journal’s guidelines. The BT-Set dataset can be accessed via: https://www.geodoi.ac.cn/916
34
edoi.aspx?DOI=10.3974/geodb.2019.04.16.V1;the UT-Set dataset can be accessed via:
https://zenodo.org/records/10246449; and the CTF-Set d917 ataset can be accessed via: https://zenodo.org/records/8148479. 918
References 919
Abdelrahim, N. A. M., & Jin, S. (2025). Genetic Algorithm Optimized Multispectral Soil-Vegetation Drought Index (GA-MSVDI) for precision 920 agriculture and drought monitoring in North Africa. Remote Sensing Applications: Society and Environment, 38, 101603. 921 https://doi.org/https://doi.org/10.1016/j.rsase.2025.101603 922
Ball, J. G. C., Hickman, S. H. M., Jackson, T. D., Koay, X. J., Hirst, J., Jay, W., Archer, M., Aubry-Kientz, M., Vincent, G., & Coomes, D. A. 923 Accurate delineation of individual tree crowns in tropical forests fro m aerial RGB imagery using Mask R-CNN. Remote Sensing in Ecology and 924 Conservation, 9(5), 641-655. 925
Bochner, S. (1949). Diffusion Equation and Stochastic Processes. Proceedings of the National Academy of Sciences, 35(7), 368-370. 926 https://doi.org/10.1073/pnas.35.7.368 927
Bolya, D., Zhou, C., Xiao, F., & Lee, Y. J. (2019, 2019/10). YOLACT: Real-Time Instance Segmentation Proceedings of the IEEE/CVF 928 International Conference on Computer Vision (ICCV), 929
Braga, J. R. G., Peripato, V. c., Dalagnol, R., Ferreira, M. P., Tarabalka, Y., Aragao, L. E. O. C., Velho, H. F. d. C., Shiguemori, E. H., & Wagner, 930 F. H. Tree crown delineation algorithm based on a convolutional neural netwo rk. 931
Brandt, M., Tucker, C. J., Kariryaa, A., Rasmussen, K., Abel, C., Small, J., Chave, J., Rasmussen, L. V., Hiernaux, P., Diouf, A. A., Kergoat, L., 932 Mertz, O., Igel, C., Gieseke, F., Schöning, J., Li, S., Melocik, K., Meyer, J., Sinno, S., . . . Fensholt, R. (2020). An unexpectedly large count of trees 933 in the West African Sahara and Sahel. Nature, 587(7832), 78-82. https://doi.org/10.1038/s41586-020-2824-5 934
Cai, Z., & Vasconcelos, N. (2021). Cascade R-CNN: High Quality Object Detection and Instance Segmentation. IEEE Transactions on Pattern 935 Analysis and Machine Intelligence, 43(5), 1483-1498. https://doi.org/10.1109/TPAMI.2019.2956516 936
Chen, K., Pang, J., Wang, J., Xiong, Y., Li, X., Sun, S., Feng, W., Liu, Z., Shi, J., Ouyang, W., Loy, C. C., & Lin, D. Hybrid Task Cascade for 937 Instance Segmentation. 938
Chen, K., Wang, J., Pang, J., Cao, Y., Xiong, Y., Li, X., & Lin, D. (2019). MMDetection: Open mmlab detection toolbox and benchmark. 939 https://arxiv.org/abs/1906.07155 940
Cheng, Y., Oehmcke, S., Brandt, M., Rosenthal, L., Das, A., Vrieling, A., Saatchi, S., Wagner, F., Mugabowindekwe, M., Verbruggen, W., Beier, 941 C., & Horion, S. (2024). Scattered tree death contributes to substantial forest loss in California. Nature Communications, 15(1), 641. 942 https://doi.org/10.1038/s41467-024-44991-z 943
Clark, M. L. (2020). Comparison of multi-seasonal Landsat 8, Sentinel-2 and hyperspectral images for mapping forest alliances in Northern 944 California. ISPRS Journal of Photogrammetry and Remote Sensing, 159, 26-40. https://doi.org/https://doi.org/10.1016/j.isprsjprs.2019.11.007 945
Cloutier, M., Germain, M., & Laliberté, E. (2024). Influence of temperate forest autumn leaf phenology on segmentation of tree species from 946 UAV imagery using deep learning. Remote Sensing of Environment, 311, 114283. https://doi.org/https://doi.org/10.1016/j.rse.2024.114283 947
Fang, Y., Yang, S., Wang, X., Li, Y., Fang, C., Shan, Y., Feng, B., & Liu, W. (2021, 2021/10). Instances As Queries Proceedings of the IEEE/CVF 948 International Conference on Computer Vision (ICCV), 949
Gilabert, M. A., González-Piqueras, J., Garcı́a-Haro, F. J., & Meliá, J. (2002). A generalized soil-adjusted vegetation index. Remote Sensing of 950 Environment, 82(2), 303-310. https://doi.org/https://doi.org/10.1016/S0034-4257(02)00048-2 951
Grieger, S., Kappas, M., Karel, S., Koal, P., Koukal, T., Löw, M., Zwanzig, M., & Putzenlechner, B. (2025). Impact of forest disturbance derived 952 from Sentinel-2 time series on Landsat 8/9 land surface temperature: The case of Norway spruce in Central Germany. ISPRS Journal of 953 Photogrammetry and Remote Sensing, 228, 388-407. https://doi.org/https://doi.org/10.1016/j.isprsjprs.2025.07.006 954
Gu, A., & Dao, T. (2024). Mamba: Linear-Time Sequence Modeling with Selective State Spaces. https://arxiv.org/abs/2312.00752 955
Gui, B., Sam, L., Bhardwaj, A., Gómez, D. S., Peñaloza, F. G., Buchroithner, M. F., & Green, D. R. (2025). SAGRNet: A novel object-based 956 graph convolutional neural network for diverse vegetation cover classification in remotely-sensed imagery. ISPRS Journal of Photogrammetry and 957 Remote Sensing, 227, 99-124. https://doi.org/https://doi.org/10.1016/j.isprsjprs.2025.06.004 958
He, K., Gkioxari, G., Dollar, P., & Girshick, R. Mask R-CNN. 959
He, X., Cao, K., Zhang, J., Yan, K., Wang, Y., Li, R., Xie, C., Hong, D., & Zhou, M. (2025). Pan-Mamba: Effective pan-sharpening with state 960
35
space model. Information Fusion, 115, 102779.
https://doi.org/https://doi.org/10.1016/j.inffus.2024.102779 961
Huang, T., Pei, X., You, S., Wang, F., Qian, C., & Xu, C. (2025, 2025//). LocalMamba: Visual State Space Model with Windowed Selective Scan. 962 Computer Vision – ECCV 2024 Workshops, Cham. 963
Hunt, E. R., Doraiswamy, P. C., McMurtrey, J. E., Daughtry, C. S. T., Perry, E. M., & Akhmedov, B. (2013). A visible band index for remote 964 sensing leaf chlorophyll content at the canopy scale. International Journal of Applied Earth Observation and Geoinformation, 21, 103-112. 965 https://doi.org/https://doi.org/10.1016/j.jag.2012.07.020 966
Jing, L., Hu, B., Noland, T., & Li, J. (2012). An individual tree crown delineation method based on multi-scale segmentation of imagery. ISPRS 967 Journal of Photogrammetry and Remote Sensing, 70, 88-98. https://doi.org/https://doi.org/10.1016/j.isprsjprs.2012.04.003 968
Kwon, R., Ryu, Y., Yang, T., Zhong, Z., & Im, J. (2023). Merging multiple sensing platforms and deep learning empowers individual tree mapping 969 and species detection at the city scale. ISPRS Journal of Photogrammetry and Remote Sensing, 206, 201-221. 970 https://doi.org/https://doi.org/10.1016/j.isprsjprs.2023.11.011 971
L, W., & W, L. (2019). Bayberry Tree Recognition Dataset Based on the Aerial Photos and Deep Learning Model. Journal of Global Change 972 Data & Discovery, 3, 290-296. https://doi.org/10.3974/geodp.2019.03.10 973
Li, S., Brandt, M., Fensholt, R., Kariryaa, A., Igel, C., Gieseke, F., Nord-Larsen, T., Oehmcke, S., Carlsen, A. H., Junttila, S., Tong, X., 974 d’Aspremont, A., & Ciais, P. (2023). Deep learning enables image-based tree counting, crown segmentation, and height prediction at national scale. 975 PNAS Nexus, 2(4), pgad076. https://doi.org/10.1093/pnasnexus/pgad076 976
Liu, Z., Kaartinen, H., Hakala, T., Hyyti, H., Hyyppä, J., Kukko, A., & Chen, R. (2025). Performance analysis of ultra-wideband positioning for 977 measuring tree positions in boreal forest plots. ISPRS Open Journal of Photogrammetry and Remote Sensing, 15, 100087. 978 https://doi.org/https://doi.org/10.1016/j.ophoto.2025.100087 979
Liu, Z., Lin, Y., Cao, Y., Hu, H., Wei, Y., Zhang, Z., Lin, S., & Guo, B. (2021, 2021/10). Swin Transformer: Hierarchical Vision Transformer 980 using Shifted Windows Proceedings of the IEEE/CVF International Conference on Computer Vision (ICCV), Montreal, Canada. 981
Lucas, R., Rowlands, A., Brown, A., Keyworth, S., & Bunting, P. (2007). Rule-based classification of multi-temporal satellite imagery for habitat 982 and agricultural land cover mapping. ISPRS Journal of Photogrammetry and Remote Sensing, 62(3), 165-185. 983 https://doi.org/https://doi.org/10.1016/j.isprsjprs.2007.03.003 984
Lugassi, R., Ben-Dor, E., & Eshel, G. (2010). A spectral-based method for reconstructing spatial distributions of soil surface temperature during 985 simulated fire events. Remote Sensing of Environment, 114(2), 322-331. https://doi.org/https://doi.org/10.1016/j.rse.2009.09.015 986
Ma, Y., Liang, S.-Z., Myers, D. B., Swatantran, A., & Lobell, D. B. (2024). Subfield-level crop yield mapping without ground truth data: A scale 987 transfer framework. Remote Sensing of Environment, 315, 114427. https://doi.org/https://doi.org/10.1016/j.rse.2024.114427 988
Meyer, G. E., & Neto, J. C. (2008). Verification of color vegetation indices for automated crop imaging applications. Computers and Electronics 989 in Agriculture, 63(2), 282-293. https://doi.org/https://doi.org/10.1016/j.compag.2008.03.009 990
Middleton, S. L. (2023). Automating image segmentation for vegetation monitoring. Nature Reviews Earth & Environment, 4(12), 807-807. 991 https://doi.org/10.1038/s43017-023-00466-1 992
Mohammadi, S., Belgiu, M., & Stein, A. (2024). A source-free unsupervised domain adaptation method for cross-regional and cross-time crop 993 mapping from satellite image time series. Remote Sensing of Environment, 314, 114385. https://doi.org/https://doi.org/10.1016/j.rse.2024.114385 994
Morton, D. C., Nagol, J., Carabajal, C. C., Rosette, J., Palace, M., Cook, B. D., Vermote, E. F., Harding, D. J., & North, P. R. (2014). Amazon 995 forests maintain consistent canopy structure and greenness during the dry season. Nature, 506(7487), 221-224. https://doi.org/10.1038/nature13006 996
Motohka, T., Nasahara, K. N., Oguma, H., & Tsuchida, S. (2010). Applicability of Green-Red Vegetation Index for Remote Sensing of Vegetation 997 Phenology. Remote Sensing, 2(10), 2369-2387. 998
Mugabowindekwe, M., Brandt, M., Chave, J., Reiner, F., Skole, D. L., Kariryaa, A., Igel, C., Hiernaux, P., Ciais, P., Mertz, O., Tong, X., Li, S., 999 Rwanyiziri, G., Dushimiyimana, T., Ndoli, A., Uwizeyimana, V., Lillesø, J.-P. B., Gieseke, F., Tucker, C. J., . . . Fensholt, R. (2023). Nation-wide 1000 mapping of tree-level aboveground carbon stocks in Rwanda. Nature Climate Change, 13(1), 91-97. https://doi.org/10.1038/s41558-022-01544-w 1001
Oberst, U. (2007). The Fast Fourier Transform. SIAM Journal on Control and Optimization, 46(2), 496-540. https://doi.org/10.1137/060658242 1002
Parshakov, I., Peddle, D., Staenz, K., Zhang, J., Coburn, C., & Cheng, H. (2025). UC–Change: a classification-based time series change detection 1003 technique for improved forest disturbance mapping using multi-sensor imagery. ISPRS Journal of Photogrammetry and Remote Sensing, 228, 370-1004
36
387.
https://doi.org/https://doi.org/10.1016/j.isprsjprs.2025.07.028 1005
Pearson, R., Phillips, S., Loranty, M., Beck, P., Damoulas, T., Knight, S., & Goetz, S. (2013). Shifts in Arctic vegetation and associated feedbacks 1006 under climate change. Nature Climate Change, 3, 673-677. https://doi.org/10.1038/nclimate1858 1007
Perkonigg, M., Hofmanninger, J., Herold, C. J., Brink, J. A., Pianykh, O., Prosch, H., & Langs, G. (2021). Dynamic memory to alleviate 1008 catastrophic forgetting in continual learning with medical imaging. Nature Communications, 12(1), 5678. https://doi.org/10.1038/s41467-021-25858-1009 z 1010
Pires, R. d. P., Olofsson, K., Persson, H. J., Lindberg, E., & Holmgren, J. (2022). Individual tree detection and estimation of stem attributes with 1011 mobile laser scanning along boreal forest roads. ISPRS Journal of Photogrammetry and Remote Sensing, 187, 211-224. 1012 https://doi.org/https://doi.org/10.1016/j.isprsjprs.2022.03.004 1013
Qi, J., Chehbouni, A., Huete, A. R., Kerr, Y. H., & Sorooshian, S. (1994). A modified soil adjusted vegetation index. Remote Sensing of 1014 Environment, 48(2), 119-126. https://doi.org/https://doi.org/10.1016/0034-4257(94)90134-1 1015
Raissi, M., Perdikaris, P., & Karniadakis, G. E. (2019). Physics-informed neural networks: A deep learning framework for solving forward and 1016 inverse problems involving nonlinear partial differential equations. Journal of Computational Physics, 378, 686-707. 1017 https://doi.org/https://doi.org/10.1016/j.jcp.2018.10.045 1018
Rees, A. (1963). Relationship between crop growth rate and leaf area index in the oil palm. Nature, 197, 63–64. https://doi.org/10.1038/197063a0 1019
Ren, H., Zhou, G., & Zhang, F. (2018). Using negative soil adjustment factor in soil-adjusted vegetation index (SAVI) for aboveground living 1020 biomass estimation in arid grasslands. Remote Sensing of Environment, 209, 439-445. https://doi.org/https://doi.org/10.1016/j.rse.2018.02.068 1021
Rere, L. M. R., Fanany, M. I., & Arymurthy, A. M. (2015). Simulated Annealing Algorithm for Deep Learning. Procedia Computer Science, 72, 1022 137-144. https://doi.org/https://doi.org/10.1016/j.procs.2015.12.114 1023
Sani-Mohammed, A., Yao, W., & Heurich, M. (2022). Instance segmentation of standing dead trees in dense forest from aerial imagery using 1024 deep learning. ISPRS Open Journal of Photogrammetry and Remote Sensing, 6, 100024. https://doi.org/https://doi.org/10.1016/j.ophoto.2022.100024 1025
Soontranon, N., Srestasathiern, P., & Rakwatin, P. (2014). Rice growing stage monitoring in small-scale region using ExG vegetation index. 1026 https://doi.org/10.1109/ECTICon.2014.6839830 1027
Soontranon, N., Srestasathiern, P., & Rakwatin, P. (2014, 14-17 May 2014). Rice growing stage monitoring in small-scale region using ExG 1028 vegetation index. 2014 11th International Conference on Electrical Engineering/Electronics, Computer, Telecommunications and Information 1029 Technology (ECTI-CON), 1030
Straker, A., Puliti, S., Breidenbach, J., Kleinn, C., Pearse, G., Astrup, R., & Magdon, P. (2023). Instance segmentation of individual tree crowns 1031 with YOLOv5: A comparison of approaches using the ForInstance benchmark LiDAR dataset. ISPRS Open Journal of Photogrammetry and Remote 1032 Sensing, 9, 100045. https://doi.org/https://doi.org/10.1016/j.ophoto.2023.100045 1033
Sun, Y., Li, Z., He, H., Guo, L., Zhang, X., & Xin, Q. (2022). Counting trees in a subtropical mega city using the instance segmentation method. 1034 International Journal of Applied Earth Observation and Geoinformation, 106, 102662. https://doi.org/https://doi.org/10.1016/j.jag.2021.102662 1035
Tollefson, J. (2009). Climate: Counting carbon in the Amazon. Nature, 461(7267), 1048-1052. https://doi.org/10.1038/4611048a 1036
Tong, F., Tong, H., Mishra, R., & Zhang, Y. (2021). Delineation of Individual Tree Crowns Using High Spatial Resolution Multispectral 1037 WorldView-3 Satellite Imagery. IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing, 14, 7751-7761. 1038 https://doi.org/10.1109/JSTARS.2021.3100748 1039
Tong, F., & Zhang, Y. (2025). Individual tree crown delineation in high resolution aerial RGB imagery using StarDist-based model. Remote 1040 Sensing of Environment, 319, 114618. https://doi.org/https://doi.org/10.1016/j.rse.2025.***********
Trumbore, S., Brando, P., & Hartmann, H. (2015). Forest health and global change. Science, 349(6250), 814-818. 1042 https://doi.org/10.1126/science.aac6759 1043
Van den Broeck, W. A. J., Terryn, L., Chen, S., Cherlet, W., Cooper, Z. T., & Calders, K. (2025). Pointwise deep learning for leaf-wood 1044 segmentation of tropical tree point clouds from terrestrial laser scanning. ISPRS Journal of Photogrammetry and Remote Sensing, 227, 366-382. 1045 https://doi.org/https://doi.org/10.1016/j.isprsjprs.2025.06.023 1046
Veitch-Michaelis, J., Cottam, A., Schweizer, D., Broadbent, E., Dao, D., Zhang, C., Almeyda Zambrano, A., & Max, S. OAM-TCD: A globally 1047 diverse dataset of high-resolution tree cover maps. Advances in Neural Information Processing Systems, 37, 49749-49767. 1048
37
Velasquez-Camacho, L., Etxegarai, M., & de-Miguel, S. (2023). Implementing Deep Learning algorithms for urban tree detection and 1049 geolocation with high-resolution aerial, satellite, and ground-level images. Computers, Environment and Urban Systems, 105, 102025. 1050 https://doi.org/https://doi.org/10.1016/j.compenvurbsys.2023.102025 1051
Veras, H. F. P., Ferreira, M. P., da Cunha Neto, E. M., Figueiredo, E. O., Corte, A. P. D., & Sanquetta, C. R. (2022). Fusing multi-season UAS 1052 images with convolutional neural networks to map tree species in Amazonian forests. Ecological Informatics, 71, 101815. 1053 https://doi.org/https://doi.org/10.1016/j.ecoinf.2022.101815 1054
Vincini, M., Frazzi, E., & D’Alessio, P. (2008). A broad-band leaf chlorophyll vegetation index at the canopy scale. Precision Agriculture, 9(5), 1055 303-319. https://doi.org/10.1007/s11119-008-9075-z 1056
Vinyals, O., Babuschkin, I., Czarnecki, W. M., Mathieu, M., Dudzik, A., Chung, J., Choi, D. H., Powell, R., Ewalds, T., Georgiev, P., Oh, J., 1057 Horgan, D., Kroiss, M., Danihelka, I., Huang, A., Sifre, L., Cai, T., Agapiou, J. P., Jaderberg, M., . . . Silver, D. (2019). Grandmaster level in StarCraft 1058 II using multi-agent reinforcement learning. Nature, 575(7782), 350-354. https://doi.org/10.1038/s41586-019-1724-z 1059
Wagner, F. H., Ferreira, M. P., Sanchez, A., Hirye, M. C. M., Zortea, M., Gloor, E., Phillips, O. L., de Souza Filho, C. R., Shimabukuro, Y. E., & 1060 Aragão, L. E. O. C. (2018). Individual tree crown delineation in a highly diverse tropical forest using very high resolution satellite images. ISPRS 1061 Journal of Photogrammetry and Remote Sensing, 145, 362-377. https://doi.org/https://doi.org/10.1016/j.isprsjprs.2018.09.013 1062
Walker, C. (2024). Patterns of forest disturbance. Nature Plants, 10(12), 1850-1850. https://doi.org/10.1038/s41477-024-01885-8 1063
Wang, L., Zhang, X., Su, H., & Zhu, J. (2024). A Comprehensive Survey of Continual Learning: Theory, Method and Application. IEEE 1064 Transactions on Pattern Analysis and Machine Intelligence, 46(8), 5362-5383. https://doi.org/10.1109/TPAMI.2024.3367329 1065
Wang, N., Guo, Y., Wei, X., Zhou, M., Wang, H., & Bai, Y. (2022). UAV-based remote sensing using visible and multispectral indices for the 1066 estimation of vegetation cover in an oasis of a desert. Ecological Indicators, 141, 109155. 1067 https://doi.org/https://doi.org/10.1016/j.ecolind.2022.109155 1068
Wang, X., Wang, Q., Lai, H., Zhang, Z., Yun, T., Lu, X., Wang, G., Lao, S., Liao, Q., Lu, S., Chen, R., Fang, S., Pan, F., Yan, H., Li, K., & Chen, 1069 B. (2025). A multi-sensor, phenology-based approach framework for mapping cassava cultivation dynamics and intercropping in highly fragmented 1070 agricultural landscapes. ISPRS Journal of Photogrammetry and Remote Sensing, 228, 44-63. 1071 https://doi.org/https://doi.org/10.1016/j.isprsjprs.2025.07.009 1072
Wang, X., Zhang, R., Kong, T., Li, L., & Shen, C. SOLOv2: Dynamic and Fast Instance Segmentation. 1073
Wei, Q., Ma, H., Chen, C., & Dong, D. (2022). Deep Reinforcement Learning With Quantum-Inspired Experience Replay. IEEE Transactions 1074 on Cybernetics, 52(9), 9326-9338. https://doi.org/10.1109/TCYB.2021.3053414 1075
Zeng, Y., Hao, D., Huete, A., Dechant, B., Berry, J., Chen, J. M., Joiner, J., Frankenberg, C., Bond-Lamberty, B., Ryu, Y., Xiao, J., Asrar, G. R., 1076 & Chen, M. (2022). Optical vegetation indices for monitoring terrestrial ecosystems globally. Nature Reviews Earth & Environment, 3(7), 477-493. 1077 https://doi.org/10.1038/s43017-022-00298-5 1078
Zhang, P., Guo, Z., Ullah, S., Melagraki, G., Afantitis, A., & Lynch, I. (2021). Nanotechnology and artificial intelligence to enable sustainable 1079 and precision agriculture. Nat Plants, 7(7), 864-876. https://doi.org/10.1038/s41477-021-00946-6 1080
Zhang, Y., Guo, Z., Wu, J., Tian, Y., Tang, H., & Guo, X. (2022). Real-Time Vehicle Detection Based on Improved YOLO v5. Sustainability, 1081 14(19). 1082
1083