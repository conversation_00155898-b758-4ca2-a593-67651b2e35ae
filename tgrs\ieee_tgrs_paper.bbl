% Generated by IEEEtran.bst, version: 1.14 (2015/08/26)
\begin{thebibliography}{10}
\providecommand{\url}[1]{#1}
\csname url@samestyle\endcsname
\providecommand{\newblock}{\relax}
\providecommand{\bibinfo}[2]{#2}
\providecommand{\BIBentrySTDinterwordspacing}{\spaceskip=0pt\relax}
\providecommand{\BIBentryALTinterwordstretchfactor}{4}
\providecommand{\BIBentryALTinterwordspacing}{\spaceskip=\fontdimen2\font plus
\BIBentryALTinterwordstretchfactor\fontdimen3\font minus
  \fontdimen4\font\relax}
\providecommand{\BIBforeignlanguage}[2]{{%
\expandafter\ifx\csname l@#1\endcsname\relax
\typeout{** WARNING: IEEEtran.bst: No hyphenation pattern has been}%
\typeout{** loaded for the language `#1'. Using the pattern for}%
\typeout{** the default language instead.}%
\else
\language=\csname l@#1\endcsname
\fi
#2}}
\providecommand{\BIBdecl}{\relax}
\BIBdecl

\bibitem{ke2011review}
Y.~Ke and <PERSON>.~<PERSON><PERSON>, ``A review of methods for automatic individual
  tree-crown detection and delineation from passive remote sensing,''
  \emph{International Journal of Remote Sensing}, vol.~32, no.~17, pp.
  4725--4747, 2011.

\bibitem{gougeon1998automatic}
F.~A. Gougeon, ``Automatic individual tree crown delineation using a
  valley-following algorithm and a rule-based system,'' \emph{Proceedings of
  the International Forum on Automated Interpretation of High Spatial
  Resolution Digital Imagery for Forestry}, pp. 11--23, 1998.

\bibitem{culvenor2002tida}
D.~S. Culvenor, ``Tida: an algorithm for the delineation of tree crowns in high
  spatial resolution remotely sensed imagery,'' \emph{Computers \&
  Geosciences}, vol.~28, no.~1, pp. 33--44, 2002.

\bibitem{erikson2003segmentation}
M.~Erikson, ``Segmentation of individual tree crowns in colour aerial
  photographs using region growing supported by fuzzy rules,'' \emph{Canadian
  Journal of Forest Research}, vol.~33, no.~8, pp. 1557--1563, 2003.

\bibitem{jing2012automated}
L.~Jing, B.~Hu, T.~Noland, and J.~Li, ``Automated delineation of individual
  tree crowns from lidar data by multi-scale analysis and segmentation,''
  \emph{Photogrammetric Engineering \& Remote Sensing}, vol.~78, no.~11, pp.
  1275--1284, 2012.

\bibitem{yang2014automated}
J.~Yang, Z.~Kang, S.~Cheng, Z.~Yang, and P.~H. Akwensi, ``Automated extraction
  of individual trees from airborne laser scanning point clouds in forests,''
  \emph{IEEE Journal of Selected Topics in Applied Earth Observations and
  Remote Sensing}, vol.~7, no.~10, pp. 4081--4094, 2014.

\bibitem{wang2004automated}
L.~Wang, P.~Gong, and G.~S. Biging, ``Automated delineation of tree crowns from
  high spatial resolution aerial imagery,'' \emph{Photogrammetric Engineering
  \& Remote Sensing}, vol.~70, no.~9, pp. 1043--1052, 2004.

\bibitem{wang2010crown}
------, ``Individual tree-crown delineation and treetop detection in
  high-spatial-resolution aerial imagery,'' \emph{Photogrammetric Engineering
  \& Remote Sensing}, vol.~70, no.~3, pp. 351--357, 2004.

\bibitem{lamar2005automated}
W.~R. Lamar, J.~B. McGraw, and T.~A. Warner, ``Automated detection of
  individual tree crowns in high resolution aerial imagery using multiple-scale
  filtering and template matching,'' \emph{Proceedings of the ASPRS 2005 Annual
  Conference}, 2005.

\bibitem{tong2021improved}
X.~Tong, K.~Wang, M.~Brandt, Y.~Yue, C.~Liao, and R.~Fensholt, ``Improved
  watershed segmentation algorithm for individual tree crown delineation using
  airborne lidar point cloud data,'' \emph{IEEE Transactions on Geoscience and
  Remote Sensing}, vol.~60, pp. 1--16, 2021.

\bibitem{zhao2023review}
K.~Zhao, J.~C. Suarez, M.~Garcia, T.~Hu, C.~Wang, and A.~G. Londo, ``A
  comprehensive review of individual tree crown detection and delineation with
  deep learning,'' \emph{ISPRS Journal of Photogrammetry and Remote Sensing},
  vol. 196, pp. 233--250, 2023.

\bibitem{lassalle2022cnn}
G.~Lassalle, M.~P. Ferreira, F.~R. Pereira, and J.-M. Sarrailh, ``Cnn-based
  individual tree crown delineation on hyperspectral images,'' \emph{IEEE
  Geoscience and Remote Sensing Letters}, vol.~19, pp. 1--5, 2022.

\bibitem{freudenberg2022individual}
M.~Freudenberg, N.~N{\"o}lke, A.~Agostini, K.~Urban, F.~W{\"o}rg{\"o}tter, and
  C.~Kleinn, ``Individual tree crown delineation in high-resolution remote
  sensing images based on u-net,'' \emph{Neural Computing and Applications},
  vol.~34, no.~22, pp. 19\,917--19\,932, 2022.

\bibitem{ronneberger2015u}
O.~Ronneberger, P.~Fischer, and T.~Brox, ``U-net: Convolutional networks for
  biomedical image segmentation,'' in \emph{International Conference on Medical
  image computing and computer-assisted intervention}.\hskip 1em plus 0.5em
  minus 0.4em\relax Springer, 2015, pp. 234--241.

\bibitem{he2017mask}
K.~He, G.~Gkioxari, P.~Doll{\'a}r, and R.~Girshick, ``Mask r-cnn,'' in
  \emph{Proceedings of the IEEE international conference on computer vision},
  2017, pp. 2961--2969.

\bibitem{braga2020amazon}
J.~R. Braga, V.~Peripato, R.~Dalagnol, M.~P. Ferreira, Y.~Tarabalka, L.~E.
  Arag{\~a}o, H.~F. de~Campos-Velho, E.~H. Shiguemori, and F.~H. Wagner, ``Tree
  crown delineation algorithm based on a convolutional neural network,''
  \emph{Remote Sensing}, vol.~12, no.~8, p. 1288, 2020.

\bibitem{hao2021individual}
Z.~Hao, L.~Lin, C.~J. Post, E.~A. Mikhailova, M.~Li, Y.~Chen, K.~Yu, and
  J.~Liu, ``Individual tree crown detection and delineation from
  very-high-resolution uav images based on bias field and marker-controlled
  watershed segmentation algorithms,'' \emph{Remote Sensing}, vol.~13, no.~7,
  p. 1298, 2021.

\bibitem{ball2023detectree2}
J.~G. Ball, S.~H. Hickman, T.~D. Jackson, X.~J. Koay, J.~Hirst, W.~Jay,
  M.~Archer, M.~Aubry-Kientz, G.~Vincent, and D.~A. Coomes, ``Detectree2: A
  software package for automated crown delineation in high-resolution aerial
  imagery,'' \emph{Methods in Ecology and Evolution}, vol.~14, no.~4, pp.
  1035--1045, 2023.

\bibitem{priyadarshan2017biology}
P.~Priyadarshan, \emph{Biology of Hevea rubber}.\hskip 1em plus 0.5em minus
  0.4em\relax CABI, 2017.

\end{thebibliography}
