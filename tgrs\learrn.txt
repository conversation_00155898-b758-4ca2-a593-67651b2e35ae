Accurately estimating forestry attributes is crucial for effective forest management and assessing the impacts of climate change. In recent years, there has been a growing interest in automatically estimating these attributes from remote sensing imagery due to the high cost and time associated with traditional field surveys. Compared to pixel-based estimation methods, individual tree-based approaches offer the potential for higher accuracy by providing more detailed information on individual trees. Well-delineated tree crowns are helpful for improving the estimation accuracy of forestry attributes like crown cover (<PERSON> et al., 2005), crown size (<PERSON> et al., 2010), diameter of breast height (DBH) (<PERSON> et al., 2018), and biomass (<PERSON> et al., 2018). However, automatically delineating individual tree crowns from remote sensing imagery remains a challenging task.
For automated delineation of individual tree crowns, two categories of methods have been developed: traditional approaches and deep learning-based approaches. According to the review by <PERSON> and Qua<PERSON>n<PERSON> (2011), traditional tree crown delineation approaches can be categorized into three main types: valley following, region growing, and watershed segmentation. Valley following relies on shaded gaps within the forest to delineate tree crowns (<PERSON><PERSON><PERSON> et al., 1998). While it performs well in mature coniferous stands, its accuracy diminishes in self-shaded tree crowns or challenging forest conditions where detecting shaded gaps becomes problematic. Region growing is also widely used for tree crown delineation. It usually utilizes the spectral characteristics of tree crowns to conduct the delineation (<PERSON><PERSON><PERSON><PERSON>, 2002, <PERSON><PERSON>, 2003, <PERSON><PERSON><PERSON> et al., 2014, <PERSON><PERSON> and <PERSON><PERSON>, 2021). Typically, the region growing algorithm requires seed points to start growing and criteria to stop growing. To get accurate seed points, it usually requires to detect treetops. However, accurately detecting treetops in complex mixed forests is difficult. To get optimal criteria to stop growing, optimal thresholds are always required to be manually determined. However, selecting a forest-wide threshold is difficult, especially for complex mixed forests. Watershed segmentation approaches are mostly conducted based on the edge information in the image. However, the performance of the tree crown delineation is negatively affected by the noise in the image, resulting in over-segmented tree crowns.
To improve traditional watershed segmentation, two groups of approaches were proposed to avoid over-segmentation in results. The first group of approaches is multiscale analysis. Jing et al. (2012) conducted multiscale watershed segmentation based on images smoothed by three dominant scales of Gaussian filters. Then optimal delineations were selected from the results of different scales. However, the three dominant scales may not adequately capture the variability in real tree crown scales. Yang et al. (2014) conducted watershed segmentation based on the calculated gradient image. Then multiscale thresholds were adopted to generate multiscale delineations. The final segmentation was determined by automatically selecting the tree crown in the optimal scale. However, since it is difficult to get the optimal scale for every tree crown, the delineation result still had a lot of over-segmented tree crowns. The second group of approaches to mitigate over-segmentation is marker-controlled watershed segmentation, where markers are detected treetops based on local maxima detection. Wang et al. (2004) and Wang (2010) detected two different types of treetops, spectral local maxima and spatial local maxima. Since both types of treetops were not perfect, the final treetops were spectral local maxima refined by spatial local maxima. Lamar et al. (2005) and Tong et al. (2021) utilized the spatial local maxima to detect treetops. After adding treetops as markers for watershed segmentation, the over-segmentations are effectively reduced. However, treetop detection always requires accurate tree crown border information, which is difficult to obtain using traditional image processing methods. Except for commonly used delineation approaches, Wagner et al. (2018) conducted tree crown delineation using mathematical morphological operations to achieve high delineation accuracy in a highly diverse tropical forest. However, since this method requires multiple manually assigned parameters, it is unsure that this method can work well for the delineations using different datasets.
More recently, deep learning-based approaches has been developed for the tree crown delineation task (Zhao et al., 2023, Ozdarici-ok and Ok, 2023, Li et al., 2023, Straker et al., 2023, Dersch et al., 2024). One category of deep learning approaches combined deep learning with watershed segmentation. For instance, Lassalle et al. (2022) utilized a CNN model to compute the distance map indicating pixels’ distances to the nearest tree crown borders. Subsequently, treetops were identified by locating local maxima in the distance map, and marker-controlled watershed segmentation was applied to produce the final delineation outcome based on the distance map and detected treetops. Similarly, Freudenberg et al. (2022) utilized the U-Net (Ronneberger et al., 2015) to predict the tree crown mask, tree crown outlines, and the distance map. The watershed segmentation was then performed based on a combination of these outputs. These approaches leverage deep learning to generate the necessary information for marker-controlled watershed segmentation, resulting in superior performance compared to traditional watershed segmentation algorithms. Another category of deep learning approaches adopts instance segmentation techniques. Specifically, MASK-RCNN (He et al., 2017) is the most widely used deep learning model for tree crown delineation research (Braga et al., 2020, Hao et al., 2021, Dersch et al., 2023, Ball et al., 2023). Braga et al. (2020) adopted the MASK R-CNN to perform tree crown detection and delineation on high-resolution satellite images from tropical forests. Hao et al. (2021) utilized the MASK R-CNN to detect discontinuous tree crowns and predict tree heights on UAV imagery. Dersch et al. (2023) evaluated the performance of MASK R-CNN for delineating tree crowns on UAV imagery collected from a mixed forest stand. Ball et al. (2023) built a model called Detectree2 based on the Mask R-CNN to delineate tree crowns in on airborne RGB imagery collected from tropical forests.

Download: Download high-res image (4MB)
Download: Download full-size image
Fig. 1. Training and testing areas in Acadia Research Forest. (For interpretation of the references to color in this figure legend, the reader is referred to the web version of this article.)


Download: Download high-res image (403KB)
Download: Download full-size image
Fig. 2. Histograms of tree crown sizes for training and testing areas.

Overall, deep learning-based delineation methods usually outperform traditional approaches, as they leverage training samples and excel at extracting information from imagery. However, several challenges remain in deep learning-based delineation methods. For the methods combining deep learning with watershed segmentation, since watershed segmentation often requires post-processing, such as noise removal or determination of markers, some manually assigned hyperparameters are required. Finding optimal values for these hyperparameters can be challenging and inconsistent across different images or environments. Although instance segmentation can avoid extra hyperparameters, it still has some problems. In instance segmentation methods like MASK R-CNN, the Non-Maximum Suppression (NMS) process is adopted to suppress redundant detections. However, because NMS relies on axis-aligned bounding boxes that do not accurately reflect the true shapes of tree crowns, it can result in incorrect delineation, especially when tree crowns overlap. Moreover, annotating tree crowns in dense mixed forests for the training of deep learning models is also challenging. Since some tree crowns in the collected imagery are indistinguishable, especially when only RGB images are available, it is difficult to accurately annotate all tree crowns in the imagery.
According to the review of existing approaches for tree crown delineation, we propose a robust and efficient tree crown delineation method based on the StarDist model (Schmidt et al., 2018), which was initially developed for cell nuclei segmentation in microscopy images. The proposed StarDist-based model focus on overcoming the problems existing in instance segmentation methods:
1.
Instead of commonly-used axis-aligned bounding boxes that fail to accurately capture tree crown shapes, star-convex polygons, which fit the characteristics of tree crowns, are adopted to improve delineation accuracy.
2.
Given the challenge of annotating all tree crowns in an image for training, we modify the loss function to allow the StarDist model to be trained with sparsely annotated tree crowns.
Since star-convex polygons can accurately capture the shapes of most tree crowns, the proposed method can generate more accurate delineation results in mixed forests. Additionally, because the model can be trained with sparsely annotated tree crowns, it is easily applicable to mixed forests for delineation. To evaluate the performance of our StarDist-based model, we applied it to delineate individual tree crowns in a complex mixed forest using commonly available RGB aerial imagery and assessed its accuracy with multiple tree crown delineation evaluation metrics. Compared to the widely recognized benchmark model MASK R-CNN, our StarDist-based model achieved an accuracy improvement of over 6% across two testing areas containing more than five different tree species. Furthermore, we analyze the impact of hyperparameters and imagery spatial resolutions on delineation accuracy to provide guidance for future users in applying the proposed model in delineation tasks.
The rest of this article is organized as follows. Section 2 provides the description of study sites, details of the proposed tree crown delineation method, and the introduction of tree crown delineation evaluation metrics. The experimental results are presented in Section 3, including the description of the delineation results, analysis of delineation errors, and the comparison with other methods. Section 4 analyzes the parameter settings of the proposed delineation method, analyzes the effect of spatial resolution, explores perspectives for forest research, and suggests possible further improvements. Section 5 provides the overall conclusion for the research.